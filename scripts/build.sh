#!/bin/bash

# AI工作台构建脚本

echo "🚀 开始构建 AI工作台..."

# 检查Node.js版本
node_version=$(node -v)
echo "📦 Node.js版本: $node_version"

# 安装依赖
echo "📥 安装依赖..."
npm install

# 类型检查
echo "🔍 进行类型检查..."
npm run type-check

# 代码检查
echo "🧹 进行代码检查..."
npm run lint

# 构建项目
echo "🔨 构建项目..."
npm run build

# 检查构建结果
if [ -d "dist" ]; then
    echo "✅ 构建成功！"
    echo "📁 构建文件位于 dist/ 目录"
    
    # 显示构建文件大小
    echo "📊 构建文件大小:"
    du -sh dist/*
    
    echo ""
    echo "🎉 AI工作台构建完成！"
    echo "📖 加载扩展步骤:"
    echo "   1. 打开 Chrome 扩展管理页面: chrome://extensions/"
    echo "   2. 开启 '开发者模式'"
    echo "   3. 点击 '加载已解压的扩展程序'"
    echo "   4. 选择项目的 dist/ 目录"
else
    echo "❌ 构建失败！"
    exit 1
fi
