{"name": "aitab-workspace-manager", "version": "1.0.0", "description": "AI工作台 - Chrome工作区管理扩展，专业的标签页管理和AI工具工作空间解决方案", "main": "dist/background.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "build:dev": "webpack --mode development", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["chrome-extension", "workspace", "tab-management", "ai-tools", "productivity"], "author": "AI工作台团队", "license": "MIT", "devDependencies": {"@types/chrome": "^0.0.268", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@typescript-eslint/eslint-plugin": "^7.7.1", "@typescript-eslint/parser": "^7.7.1", "autoprefixer": "^10.4.19", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.9.0", "postcss": "^8.4.38", "postcss-loader": "^8.1.1", "tailwindcss": "^3.4.3", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "lucide-react": "^0.376.0", "clsx": "^2.1.1"}}