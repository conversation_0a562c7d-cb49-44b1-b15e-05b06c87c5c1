import { WorkspaceTab, TabGroup } from '@/types';

/**
 * 标签页管理服务类
 * 负责标签页的创建、挂起、恢复、分组等功能
 */
export class TabService {
  private static instance: TabService;

  public static getInstance(): TabService {
    if (!TabService.instance) {
      TabService.instance = new TabService();
    }
    return TabService.instance;
  }

  /**
   * 获取当前窗口的所有标签页
   */
  async getCurrentTabs(): Promise<WorkspaceTab[]> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      
      return tabs.map((tab, index) => ({
        id: tab.id?.toString() || '',
        url: tab.url || '',
        title: tab.title || '',
        favIconUrl: tab.favIconUrl,
        isPinned: tab.pinned || false,
        isSuspended: tab.discarded || false,
        groupId: tab.groupId !== -1 ? tab.groupId?.toString() : undefined,
        position: index,
        lastAccessed: Date.now()
      }));
    } catch (error) {
      console.error('获取当前标签页失败:', error);
      return [];
    }
  }

  /**
   * 创建新标签页
   */
  async createTab(
    url: string, 
    workspaceId: string, 
    options?: { pinned?: boolean; active?: boolean }
  ): Promise<chrome.tabs.Tab | null> {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned: options?.pinned || false,
        active: options?.active || false
      });

      console.log(`在工作区 ${workspaceId} 中创建标签页: ${url}`);
      return tab;
    } catch (error) {
      console.error('创建标签页失败:', error);
      return null;
    }
  }

  /**
   * 关闭非固定标签页
   */
  async closeNonPinnedTabs(): Promise<void> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true, pinned: false });
      const tabIds = tabs.map(tab => tab.id).filter(id => id !== undefined) as number[];
      
      if (tabIds.length > 0) {
        await chrome.tabs.remove(tabIds);
        console.log(`关闭了 ${tabIds.length} 个非固定标签页`);
      }
    } catch (error) {
      console.error('关闭非固定标签页失败:', error);
    }
  }

  /**
   * 关闭工作区的所有标签页
   */
  async closeWorkspaceTabs(workspaceId: string): Promise<void> {
    try {
      // 这里需要根据实际的标签页跟踪机制来实现
      // 暂时关闭所有非固定标签页
      await this.closeNonPinnedTabs();
      console.log(`关闭工作区 ${workspaceId} 的所有标签页`);
    } catch (error) {
      console.error('关闭工作区标签页失败:', error);
    }
  }

  /**
   * 挂起标签页
   */
  async suspendTab(tabId: number): Promise<void> {
    try {
      await chrome.tabs.discard(tabId);
      console.log(`标签页 ${tabId} 已挂起`);
    } catch (error) {
      console.error('挂起标签页失败:', error);
    }
  }

  /**
   * 挂起多个标签页
   */
  async suspendTabs(tabIds: number[]): Promise<void> {
    try {
      for (const tabId of tabIds) {
        await this.suspendTab(tabId);
      }
      console.log(`挂起了 ${tabIds.length} 个标签页`);
    } catch (error) {
      console.error('批量挂起标签页失败:', error);
    }
  }

  /**
   * 挂起当前工作区的非活跃标签页
   */
  async suspendInactiveTabs(excludeTabId?: number): Promise<void> {
    try {
      const tabs = await chrome.tabs.query({ 
        currentWindow: true, 
        pinned: false,
        discarded: false 
      });

      const tabsToSuspend = tabs.filter(tab => 
        tab.id !== excludeTabId && !tab.active
      );

      const tabIds = tabsToSuspend.map(tab => tab.id).filter(id => id !== undefined) as number[];
      
      if (tabIds.length > 0) {
        await this.suspendTabs(tabIds);
      }
    } catch (error) {
      console.error('挂起非活跃标签页失败:', error);
    }
  }

  /**
   * 固定标签页
   */
  async pinTab(tabId: number): Promise<void> {
    try {
      await chrome.tabs.update(tabId, { pinned: true });
      console.log(`标签页 ${tabId} 已固定`);
    } catch (error) {
      console.error('固定标签页失败:', error);
    }
  }

  /**
   * 取消固定标签页
   */
  async unpinTab(tabId: number): Promise<void> {
    try {
      await chrome.tabs.update(tabId, { pinned: false });
      console.log(`标签页 ${tabId} 已取消固定`);
    } catch (error) {
      console.error('取消固定标签页失败:', error);
    }
  }

  /**
   * 根据URL模式自动固定标签页
   */
  async autoPinTabs(patterns: string[]): Promise<void> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      
      for (const tab of tabs) {
        if (tab.url && tab.id && !tab.pinned) {
          const shouldPin = patterns.some(pattern => 
            this.matchesPattern(tab.url!, pattern)
          );
          
          if (shouldPin) {
            await this.pinTab(tab.id);
          }
        }
      }
    } catch (error) {
      console.error('自动固定标签页失败:', error);
    }
  }

  /**
   * 创建标签页分组
   */
  async createTabGroup(
    tabIds: number[], 
    groupName: string, 
    color: chrome.tabGroups.ColorEnum = 'blue'
  ): Promise<number | null> {
    try {
      const groupId = await chrome.tabs.group({ tabIds });
      await chrome.tabGroups.update(groupId, {
        title: groupName,
        color: color
      });
      
      console.log(`创建标签页分组 "${groupName}"`);
      return groupId;
    } catch (error) {
      console.error('创建标签页分组失败:', error);
      return null;
    }
  }

  /**
   * 检测重复标签页
   */
  async detectDuplicateTabs(): Promise<chrome.tabs.Tab[][]> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const urlGroups = new Map<string, chrome.tabs.Tab[]>();
      
      // 按URL分组
      for (const tab of tabs) {
        if (tab.url) {
          const normalizedUrl = this.normalizeUrl(tab.url);
          if (!urlGroups.has(normalizedUrl)) {
            urlGroups.set(normalizedUrl, []);
          }
          urlGroups.get(normalizedUrl)!.push(tab);
        }
      }
      
      // 返回有重复的分组
      return Array.from(urlGroups.values()).filter(group => group.length > 1);
    } catch (error) {
      console.error('检测重复标签页失败:', error);
      return [];
    }
  }

  /**
   * 关闭重复标签页（保留最新的）
   */
  async closeDuplicateTabs(): Promise<void> {
    try {
      const duplicateGroups = await this.detectDuplicateTabs();
      
      for (const group of duplicateGroups) {
        // 按最后访问时间排序，保留最新的
        const sortedTabs = group.sort((a, b) => 
          (b.lastAccessed || 0) - (a.lastAccessed || 0)
        );
        
        // 关闭除第一个外的所有标签页
        const tabsToClose = sortedTabs.slice(1);
        const tabIds = tabsToClose.map(tab => tab.id).filter(id => id !== undefined) as number[];
        
        if (tabIds.length > 0) {
          await chrome.tabs.remove(tabIds);
        }
      }
      
      console.log(`处理了 ${duplicateGroups.length} 组重复标签页`);
    } catch (error) {
      console.error('关闭重复标签页失败:', error);
    }
  }

  /**
   * 检查URL是否匹配模式
   */
  private matchesPattern(url: string, pattern: string): boolean {
    try {
      // 简单的通配符匹配
      const regexPattern = pattern
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.');
      
      const regex = new RegExp(regexPattern, 'i');
      return regex.test(url);
    } catch (error) {
      console.error('URL模式匹配失败:', error);
      return false;
    }
  }

  /**
   * 标准化URL（移除查询参数和片段）
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
    } catch (error) {
      return url;
    }
  }
}
