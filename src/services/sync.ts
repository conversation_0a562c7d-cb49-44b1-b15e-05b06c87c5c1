import { AppState, Workspace, AppSettings } from '@/types';
import { StorageService } from './storage';

/**
 * 数据同步服务类
 * 负责数据的备份、恢复和同步功能
 */
export class SyncService {
  private static instance: SyncService;
  private storageService: StorageService;

  constructor() {
    this.storageService = StorageService.getInstance();
  }

  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  /**
   * 导出所有数据
   */
  async exportData(): Promise<string> {
    try {
      const appState = await this.storageService.getAppState();
      const presets = await this.storageService.getPresetWorkspaces();
      
      const exportData = {
        version: '1.0.0',
        timestamp: Date.now(),
        appState,
        presets,
        metadata: {
          totalWorkspaces: appState.workspaces.length,
          totalPresets: presets.length,
          exportedBy: 'AI工作台 v1.0.0'
        }
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('导出数据失败:', error);
      throw error;
    }
  }

  /**
   * 导入数据
   */
  async importData(jsonData: string, options: {
    mergeWorkspaces?: boolean;
    mergePresets?: boolean;
    overwriteSettings?: boolean;
  } = {}): Promise<void> {
    try {
      const importData = JSON.parse(jsonData);
      
      // 验证数据格式
      if (!this.validateImportData(importData)) {
        throw new Error('导入数据格式无效');
      }

      const {
        mergeWorkspaces = false,
        mergePresets = false,
        overwriteSettings = false
      } = options;

      // 导入工作区
      if (importData.appState?.workspaces) {
        await this.importWorkspaces(importData.appState.workspaces, mergeWorkspaces);
      }

      // 导入预设
      if (importData.presets) {
        await this.importPresets(importData.presets, mergePresets);
      }

      // 导入设置
      if (importData.appState?.settings && overwriteSettings) {
        await this.storageService.saveAppSettings(importData.appState.settings);
      }

      console.log('数据导入成功');
    } catch (error) {
      console.error('导入数据失败:', error);
      throw error;
    }
  }

  /**
   * 创建数据备份
   */
  async createBackup(): Promise<void> {
    try {
      const exportData = await this.exportData();
      const backupKey = `aitab_backup_${Date.now()}`;
      
      await chrome.storage.local.set({
        [backupKey]: exportData
      });

      // 保持最多5个备份
      await this.cleanupOldBackups();
      
      console.log('数据备份创建成功');
    } catch (error) {
      console.error('创建备份失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有备份
   */
  async getBackups(): Promise<Array<{
    key: string;
    timestamp: number;
    size: number;
  }>> {
    try {
      const result = await chrome.storage.local.get();
      const backups: Array<{
        key: string;
        timestamp: number;
        size: number;
      }> = [];

      for (const [key, value] of Object.entries(result)) {
        if (key.startsWith('aitab_backup_')) {
          const timestamp = parseInt(key.replace('aitab_backup_', ''));
          const size = JSON.stringify(value).length;
          backups.push({ key, timestamp, size });
        }
      }

      return backups.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('获取备份列表失败:', error);
      return [];
    }
  }

  /**
   * 恢复备份
   */
  async restoreBackup(backupKey: string): Promise<void> {
    try {
      const result = await chrome.storage.local.get(backupKey);
      const backupData = result[backupKey];
      
      if (!backupData) {
        throw new Error('备份数据不存在');
      }

      await this.importData(backupData, {
        mergeWorkspaces: false,
        mergePresets: false,
        overwriteSettings: true
      });

      console.log('备份恢复成功');
    } catch (error) {
      console.error('恢复备份失败:', error);
      throw error;
    }
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupKey: string): Promise<void> {
    try {
      await chrome.storage.local.remove(backupKey);
      console.log('备份删除成功');
    } catch (error) {
      console.error('删除备份失败:', error);
      throw error;
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage(): Promise<{
    used: number;
    total: number;
    percentage: number;
    breakdown: Record<string, number>;
  }> {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      
      // 获取各部分数据大小
      const result = await chrome.storage.local.get();
      const breakdown: Record<string, number> = {};
      
      for (const [key, value] of Object.entries(result)) {
        breakdown[key] = JSON.stringify(value).length;
      }

      return {
        used: usage,
        total: quota,
        percentage: (usage / quota) * 100,
        breakdown
      };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      return {
        used: 0,
        total: 0,
        percentage: 0,
        breakdown: {}
      };
    }
  }

  /**
   * 清理存储空间
   */
  async cleanupStorage(): Promise<void> {
    try {
      // 清理旧备份
      await this.cleanupOldBackups();
      
      // 清理临时数据
      const result = await chrome.storage.local.get();
      const keysToRemove: string[] = [];
      
      for (const key of Object.keys(result)) {
        if (key.startsWith('temp_') || key.startsWith('cache_')) {
          keysToRemove.push(key);
        }
      }
      
      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
      }
      
      console.log('存储空间清理完成');
    } catch (error) {
      console.error('清理存储空间失败:', error);
      throw error;
    }
  }

  /**
   * 验证导入数据格式
   */
  private validateImportData(data: any): boolean {
    try {
      return (
        data &&
        typeof data === 'object' &&
        data.version &&
        data.timestamp &&
        (data.appState || data.presets)
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * 导入工作区
   */
  private async importWorkspaces(workspaces: Workspace[], merge: boolean): Promise<void> {
    try {
      if (merge) {
        const existingWorkspaces = await this.storageService.getWorkspaces();
        const mergedWorkspaces = [...existingWorkspaces];
        
        for (const workspace of workspaces) {
          const existingIndex = mergedWorkspaces.findIndex(w => w.id === workspace.id);
          if (existingIndex >= 0) {
            mergedWorkspaces[existingIndex] = workspace;
          } else {
            mergedWorkspaces.push(workspace);
          }
        }
        
        await this.storageService.saveWorkspaces(mergedWorkspaces);
      } else {
        await this.storageService.saveWorkspaces(workspaces);
      }
    } catch (error) {
      console.error('导入工作区失败:', error);
      throw error;
    }
  }

  /**
   * 导入预设
   */
  private async importPresets(presets: any[], merge: boolean): Promise<void> {
    try {
      if (merge) {
        const existingPresets = await this.storageService.getPresetWorkspaces();
        const mergedPresets = [...existingPresets];
        
        for (const preset of presets) {
          const existingIndex = mergedPresets.findIndex(p => p.id === preset.id);
          if (existingIndex >= 0) {
            mergedPresets[existingIndex] = preset;
          } else {
            mergedPresets.push(preset);
          }
        }
        
        await chrome.storage.local.set({
          aitab_preset_workspaces: mergedPresets
        });
      } else {
        await chrome.storage.local.set({
          aitab_preset_workspaces: presets
        });
      }
    } catch (error) {
      console.error('导入预设失败:', error);
      throw error;
    }
  }

  /**
   * 清理旧备份
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const backups = await this.getBackups();
      
      // 保留最新的5个备份
      if (backups.length > 5) {
        const oldBackups = backups.slice(5);
        const keysToRemove = oldBackups.map(backup => backup.key);
        await chrome.storage.local.remove(keysToRemove);
      }
    } catch (error) {
      console.error('清理旧备份失败:', error);
    }
  }
}
