import { 
  Workspace, 
  AppState, 
  AppSettings, 
  STORAGE_KEYS, 
  PRESET_WORKSPACES,
  PresetWorkspace 
} from '@/types';

/**
 * Chrome存储服务类
 * 负责所有数据的持久化存储和读取
 */
export class StorageService {
  private static instance: StorageService;

  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }

  /**
   * 获取所有工作区
   */
  async getWorkspaces(): Promise<Workspace[]> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      return result[STORAGE_KEYS.WORKSPACES] || [];
    } catch (error) {
      console.error('获取工作区失败:', error);
      return [];
    }
  }

  /**
   * 保存工作区列表
   */
  async saveWorkspaces(workspaces: Workspace[]): Promise<void> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces
      });
    } catch (error) {
      console.error('保存工作区失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前活跃工作区ID
   */
  async getActiveWorkspaceId(): Promise<string | null> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE);
      return result[STORAGE_KEYS.ACTIVE_WORKSPACE] || null;
    } catch (error) {
      console.error('获取活跃工作区失败:', error);
      return null;
    }
  }

  /**
   * 设置当前活跃工作区ID
   */
  async setActiveWorkspaceId(workspaceId: string | null): Promise<void> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE]: workspaceId
      });
    } catch (error) {
      console.error('设置活跃工作区失败:', error);
      throw error;
    }
  }

  /**
   * 获取应用设置
   */
  async getAppSettings(): Promise<AppSettings> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.APP_SETTINGS);
      return result[STORAGE_KEYS.APP_SETTINGS] || this.getDefaultSettings();
    } catch (error) {
      console.error('获取应用设置失败:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * 保存应用设置
   */
  async saveAppSettings(settings: AppSettings): Promise<void> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.APP_SETTINGS]: settings
      });
    } catch (error) {
      console.error('保存应用设置失败:', error);
      throw error;
    }
  }

  /**
   * 获取预设工作区
   */
  async getPresetWorkspaces(): Promise<PresetWorkspace[]> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.PRESET_WORKSPACES);
      return result[STORAGE_KEYS.PRESET_WORKSPACES] || PRESET_WORKSPACES;
    } catch (error) {
      console.error('获取预设工作区失败:', error);
      return PRESET_WORKSPACES;
    }
  }

  /**
   * 获取完整应用状态
   */
  async getAppState(): Promise<AppState> {
    try {
      const [workspaces, activeWorkspaceId, settings] = await Promise.all([
        this.getWorkspaces(),
        this.getActiveWorkspaceId(),
        this.getAppSettings()
      ]);

      return {
        workspaces,
        activeWorkspaceId,
        settings,
        lastSync: Date.now()
      };
    } catch (error) {
      console.error('获取应用状态失败:', error);
      throw error;
    }
  }

  /**
   * 保存完整应用状态
   */
  async saveAppState(state: Partial<AppState>): Promise<void> {
    try {
      const updates: Record<string, any> = {};
      
      if (state.workspaces) {
        updates[STORAGE_KEYS.WORKSPACES] = state.workspaces;
      }
      
      if (state.activeWorkspaceId !== undefined) {
        updates[STORAGE_KEYS.ACTIVE_WORKSPACE] = state.activeWorkspaceId;
      }
      
      if (state.settings) {
        updates[STORAGE_KEYS.APP_SETTINGS] = state.settings;
      }

      await chrome.storage.local.set(updates);
    } catch (error) {
      console.error('保存应用状态失败:', error);
      throw error;
    }
  }

  /**
   * 清除所有数据
   */
  async clearAll(): Promise<void> {
    try {
      await chrome.storage.local.clear();
    } catch (error) {
      console.error('清除数据失败:', error);
      throw error;
    }
  }

  /**
   * 初始化默认数据
   */
  async initializeDefaults(): Promise<void> {
    try {
      const existingWorkspaces = await this.getWorkspaces();
      if (existingWorkspaces.length === 0) {
        // 如果没有工作区，创建一个默认工作区
        const defaultWorkspace: Workspace = {
          id: 'default',
          name: '默认工作区',
          description: '系统默认工作区',
          color: '#6b7280',
          tabs: [],
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          settings: {
            autoSuspend: false,
            suspendTimeout: 30,
            autoPinPatterns: [],
            allowDuplicates: true,
            syncEnabled: true
          }
        };

        await this.saveWorkspaces([defaultWorkspace]);
        await this.setActiveWorkspaceId('default');
      }

      // 确保预设工作区已保存
      const existingPresets = await chrome.storage.local.get(STORAGE_KEYS.PRESET_WORKSPACES);
      if (!existingPresets[STORAGE_KEYS.PRESET_WORKSPACES]) {
        await chrome.storage.local.set({
          [STORAGE_KEYS.PRESET_WORKSPACES]: PRESET_WORKSPACES
        });
      }
    } catch (error) {
      console.error('初始化默认数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取默认应用设置
   */
  private getDefaultSettings(): AppSettings {
    return {
      theme: 'auto',
      language: 'zh-CN',
      shortcuts: {
        'switch-workspace': 'Ctrl+Shift+W',
        'create-workspace': 'Ctrl+Shift+N',
        'suspend-tabs': 'Ctrl+Shift+S'
      },
      notifications: {
        enabled: true,
        suspendNotification: true,
        workspaceSwitchNotification: false
      },
      privacy: {
        collectAnalytics: false,
        shareUsageData: false
      }
    };
  }
}
