import { PresetWorkspace, Workspace, WorkspaceCategory } from '@/types';
import { StorageService } from './storage';
import { WorkspaceService } from './workspace';

/**
 * 预设工作区服务类
 * 负责管理预设工作区的创建、更新和应用
 */
export class PresetService {
  private static instance: PresetService;
  private storageService: StorageService;
  private workspaceService: WorkspaceService;

  constructor() {
    this.storageService = StorageService.getInstance();
    this.workspaceService = WorkspaceService.getInstance();
  }

  public static getInstance(): PresetService {
    if (!PresetService.instance) {
      PresetService.instance = new PresetService();
    }
    return PresetService.instance;
  }

  /**
   * 获取所有预设工作区
   */
  async getPresets(): Promise<PresetWorkspace[]> {
    try {
      return await this.storageService.getPresetWorkspaces();
    } catch (error) {
      console.error('获取预设工作区失败:', error);
      return [];
    }
  }

  /**
   * 根据分类获取预设工作区
   */
  async getPresetsByCategory(category: WorkspaceCategory): Promise<PresetWorkspace[]> {
    try {
      const presets = await this.getPresets();
      return presets.filter(preset => preset.category === category);
    } catch (error) {
      console.error('根据分类获取预设工作区失败:', error);
      return [];
    }
  }

  /**
   * 从预设创建工作区
   */
  async createWorkspaceFromPreset(presetId: string): Promise<Workspace> {
    try {
      const presets = await this.getPresets();
      const preset = presets.find(p => p.id === presetId);
      
      if (!preset) {
        throw new Error(`预设工作区 ${presetId} 不存在`);
      }

      // 创建工作区
      const workspace = await this.workspaceService.createWorkspace(
        preset.name,
        preset.description,
        preset.color,
        preset.settings
      );

      // 打开预设的URL
      await this.openPresetUrls(preset.urls);

      console.log(`从预设 "${preset.name}" 创建工作区成功`);
      return workspace;
    } catch (error) {
      console.error('从预设创建工作区失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建多个预设工作区
   */
  async createMultipleWorkspacesFromPresets(presetIds: string[]): Promise<Workspace[]> {
    try {
      const workspaces: Workspace[] = [];
      
      for (const presetId of presetIds) {
        const workspace = await this.createWorkspaceFromPreset(presetId);
        workspaces.push(workspace);
      }
      
      console.log(`批量创建了 ${workspaces.length} 个工作区`);
      return workspaces;
    } catch (error) {
      console.error('批量创建工作区失败:', error);
      throw error;
    }
  }

  /**
   * 创建AI工具套件（主力+次选）
   */
  async createAIToolsSuite(): Promise<Workspace[]> {
    try {
      const aiPresets = await this.getPresetsByCategory('ai-tools');
      const presetIds = aiPresets.map(preset => preset.id);
      
      return await this.createMultipleWorkspacesFromPresets(presetIds);
    } catch (error) {
      console.error('创建AI工具套件失败:', error);
      throw error;
    }
  }

  /**
   * 创建完整工作环境（AI工具+技术论坛+协作工具）
   */
  async createCompleteWorkEnvironment(): Promise<Workspace[]> {
    try {
      const presets = await this.getPresets();
      const essentialPresets = presets.filter(preset => 
        ['ai-tools', 'tech-forums', 'collaboration'].includes(preset.category)
      );
      
      const presetIds = essentialPresets.map(preset => preset.id);
      return await this.createMultipleWorkspacesFromPresets(presetIds);
    } catch (error) {
      console.error('创建完整工作环境失败:', error);
      throw error;
    }
  }

  /**
   * 添加自定义预设工作区
   */
  async addCustomPreset(preset: Omit<PresetWorkspace, 'id'>): Promise<PresetWorkspace> {
    try {
      const presets = await this.getPresets();
      
      const newPreset: PresetWorkspace = {
        ...preset,
        id: this.generatePresetId(preset.name)
      };
      
      presets.push(newPreset);
      await this.savePresets(presets);
      
      console.log(`添加自定义预设工作区: ${newPreset.name}`);
      return newPreset;
    } catch (error) {
      console.error('添加自定义预设工作区失败:', error);
      throw error;
    }
  }

  /**
   * 更新预设工作区
   */
  async updatePreset(
    presetId: string, 
    updates: Partial<Omit<PresetWorkspace, 'id'>>
  ): Promise<void> {
    try {
      const presets = await this.getPresets();
      const presetIndex = presets.findIndex(p => p.id === presetId);
      
      if (presetIndex === -1) {
        throw new Error(`预设工作区 ${presetId} 不存在`);
      }
      
      presets[presetIndex] = {
        ...presets[presetIndex],
        ...updates
      };
      
      await this.savePresets(presets);
      console.log(`更新预设工作区 ${presetId} 成功`);
    } catch (error) {
      console.error('更新预设工作区失败:', error);
      throw error;
    }
  }

  /**
   * 删除预设工作区
   */
  async deletePreset(presetId: string): Promise<void> {
    try {
      const presets = await this.getPresets();
      const filteredPresets = presets.filter(p => p.id !== presetId);
      
      if (filteredPresets.length === presets.length) {
        throw new Error(`预设工作区 ${presetId} 不存在`);
      }
      
      await this.savePresets(filteredPresets);
      console.log(`删除预设工作区 ${presetId} 成功`);
    } catch (error) {
      console.error('删除预设工作区失败:', error);
      throw error;
    }
  }

  /**
   * 从现有工作区创建预设
   */
  async createPresetFromWorkspace(
    workspace: Workspace,
    category: WorkspaceCategory = 'custom'
  ): Promise<PresetWorkspace> {
    try {
      const preset: PresetWorkspace = {
        id: this.generatePresetId(workspace.name),
        name: workspace.name,
        description: workspace.description || '',
        color: workspace.color,
        icon: workspace.icon || '📁',
        category,
        urls: workspace.tabs.map(tab => tab.url),
        settings: workspace.settings
      };
      
      const presets = await this.getPresets();
      presets.push(preset);
      await this.savePresets(presets);
      
      console.log(`从工作区 "${workspace.name}" 创建预设成功`);
      return preset;
    } catch (error) {
      console.error('从工作区创建预设失败:', error);
      throw error;
    }
  }

  /**
   * 获取预设工作区的统计信息
   */
  async getPresetStats(): Promise<{
    total: number;
    byCategory: Record<WorkspaceCategory, number>;
    totalUrls: number;
  }> {
    try {
      const presets = await this.getPresets();
      
      const byCategory = presets.reduce((acc, preset) => {
        acc[preset.category] = (acc[preset.category] || 0) + 1;
        return acc;
      }, {} as Record<WorkspaceCategory, number>);
      
      const totalUrls = presets.reduce((sum, preset) => sum + preset.urls.length, 0);
      
      return {
        total: presets.length,
        byCategory,
        totalUrls
      };
    } catch (error) {
      console.error('获取预设统计信息失败:', error);
      return {
        total: 0,
        byCategory: {} as Record<WorkspaceCategory, number>,
        totalUrls: 0
      };
    }
  }

  /**
   * 重置为默认预设
   */
  async resetToDefaults(): Promise<void> {
    try {
      const { PRESET_WORKSPACES } = await import('@/types');
      await this.savePresets(PRESET_WORKSPACES);
      console.log('重置为默认预设工作区成功');
    } catch (error) {
      console.error('重置为默认预设失败:', error);
      throw error;
    }
  }

  /**
   * 打开预设的URL列表
   */
  private async openPresetUrls(urls: string[]): Promise<void> {
    try {
      for (const url of urls) {
        await chrome.tabs.create({
          url,
          active: false // 在后台打开
        });
      }
    } catch (error) {
      console.error('打开预设URL失败:', error);
    }
  }

  /**
   * 保存预设工作区列表
   */
  private async savePresets(presets: PresetWorkspace[]): Promise<void> {
    try {
      await chrome.storage.local.set({
        aitab_preset_workspaces: presets
      });
    } catch (error) {
      console.error('保存预设工作区失败:', error);
      throw error;
    }
  }

  /**
   * 生成预设工作区ID
   */
  private generatePresetId(name: string): string {
    const timestamp = Date.now();
    const sanitizedName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    return `preset-${sanitizedName}-${timestamp}`;
  }
}
