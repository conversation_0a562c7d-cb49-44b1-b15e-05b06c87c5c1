import { 
  Workspace, 
  WorkspaceTab, 
  WorkspaceSettings, 
  PresetWorkspace,
  TabGroup 
} from '@/types';
import { StorageService } from './storage';
import { TabService } from './tabs';

/**
 * 工作区管理服务类
 * 负责工作区的创建、切换、删除等核心功能
 */
export class WorkspaceService {
  private static instance: WorkspaceService;
  private storageService: StorageService;
  private tabService: TabService;

  constructor() {
    this.storageService = StorageService.getInstance();
    this.tabService = TabService.getInstance();
  }

  public static getInstance(): WorkspaceService {
    if (!WorkspaceService.instance) {
      WorkspaceService.instance = new WorkspaceService();
    }
    return WorkspaceService.instance;
  }

  /**
   * 创建新工作区
   */
  async createWorkspace(
    name: string, 
    description?: string, 
    color: string = '#6b7280',
    settings?: Partial<WorkspaceSettings>
  ): Promise<Workspace> {
    try {
      const workspaces = await this.storageService.getWorkspaces();
      
      // 生成唯一ID
      const id = this.generateWorkspaceId();
      
      // 创建新工作区
      const newWorkspace: Workspace = {
        id,
        name,
        description,
        color,
        tabs: [],
        isActive: false,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        settings: {
          autoSuspend: true,
          suspendTimeout: 30,
          autoPinPatterns: [],
          allowDuplicates: false,
          syncEnabled: true,
          ...settings
        }
      };

      // 添加到工作区列表
      workspaces.push(newWorkspace);
      await this.storageService.saveWorkspaces(workspaces);

      console.log(`工作区 "${name}" 创建成功`);
      return newWorkspace;
    } catch (error) {
      console.error('创建工作区失败:', error);
      throw error;
    }
  }

  /**
   * 从预设创建工作区
   */
  async createWorkspaceFromPreset(presetId: string): Promise<Workspace> {
    try {
      const presets = await this.storageService.getPresetWorkspaces();
      const preset = presets.find(p => p.id === presetId);
      
      if (!preset) {
        throw new Error(`预设工作区 ${presetId} 不存在`);
      }

      // 创建工作区
      const workspace = await this.createWorkspace(
        preset.name,
        preset.description,
        preset.color,
        preset.settings
      );

      // 打开预设的URL
      for (const url of preset.urls) {
        await this.tabService.createTab(url, workspace.id);
      }

      console.log(`从预设 "${preset.name}" 创建工作区成功`);
      return workspace;
    } catch (error) {
      console.error('从预设创建工作区失败:', error);
      throw error;
    }
  }

  /**
   * 切换到指定工作区
   */
  async switchToWorkspace(workspaceId: string): Promise<void> {
    try {
      const workspaces = await this.storageService.getWorkspaces();
      const targetWorkspace = workspaces.find(w => w.id === workspaceId);
      
      if (!targetWorkspace) {
        throw new Error(`工作区 ${workspaceId} 不存在`);
      }

      // 保存当前工作区状态
      const currentWorkspaceId = await this.storageService.getActiveWorkspaceId();
      if (currentWorkspaceId) {
        await this.saveCurrentWorkspaceState(currentWorkspaceId);
      }

      // 关闭当前所有标签页（除了固定的）
      await this.tabService.closeNonPinnedTabs();

      // 恢复目标工作区的标签页
      await this.restoreWorkspaceTabs(targetWorkspace);

      // 更新活跃工作区
      await this.storageService.setActiveWorkspaceId(workspaceId);

      // 更新工作区状态
      const updatedWorkspaces = workspaces.map(w => ({
        ...w,
        isActive: w.id === workspaceId,
        updatedAt: w.id === workspaceId ? Date.now() : w.updatedAt
      }));

      await this.storageService.saveWorkspaces(updatedWorkspaces);

      console.log(`切换到工作区 "${targetWorkspace.name}" 成功`);
    } catch (error) {
      console.error('切换工作区失败:', error);
      throw error;
    }
  }

  /**
   * 删除工作区
   */
  async deleteWorkspace(workspaceId: string): Promise<void> {
    try {
      const workspaces = await this.storageService.getWorkspaces();
      const workspaceIndex = workspaces.findIndex(w => w.id === workspaceId);
      
      if (workspaceIndex === -1) {
        throw new Error(`工作区 ${workspaceId} 不存在`);
      }

      const workspace = workspaces[workspaceIndex];
      
      // 如果删除的是当前活跃工作区，需要切换到其他工作区
      const currentWorkspaceId = await this.storageService.getActiveWorkspaceId();
      if (currentWorkspaceId === workspaceId) {
        // 关闭当前工作区的所有标签页
        await this.tabService.closeWorkspaceTabs(workspaceId);
        
        // 切换到第一个可用的工作区
        const remainingWorkspaces = workspaces.filter(w => w.id !== workspaceId);
        if (remainingWorkspaces.length > 0) {
          await this.switchToWorkspace(remainingWorkspaces[0].id);
        } else {
          // 如果没有其他工作区，创建一个默认工作区
          const defaultWorkspace = await this.createWorkspace('默认工作区');
          await this.switchToWorkspace(defaultWorkspace.id);
        }
      }

      // 从列表中移除工作区
      workspaces.splice(workspaceIndex, 1);
      await this.storageService.saveWorkspaces(workspaces);

      console.log(`工作区 "${workspace.name}" 删除成功`);
    } catch (error) {
      console.error('删除工作区失败:', error);
      throw error;
    }
  }

  /**
   * 更新工作区信息
   */
  async updateWorkspace(
    workspaceId: string, 
    updates: Partial<Pick<Workspace, 'name' | 'description' | 'color' | 'settings'>>
  ): Promise<void> {
    try {
      const workspaces = await this.storageService.getWorkspaces();
      const workspaceIndex = workspaces.findIndex(w => w.id === workspaceId);
      
      if (workspaceIndex === -1) {
        throw new Error(`工作区 ${workspaceId} 不存在`);
      }

      // 更新工作区信息
      workspaces[workspaceIndex] = {
        ...workspaces[workspaceIndex],
        ...updates,
        updatedAt: Date.now()
      };

      await this.storageService.saveWorkspaces(workspaces);
      console.log(`工作区 ${workspaceId} 更新成功`);
    } catch (error) {
      console.error('更新工作区失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前活跃工作区
   */
  async getActiveWorkspace(): Promise<Workspace | null> {
    try {
      const activeWorkspaceId = await this.storageService.getActiveWorkspaceId();
      if (!activeWorkspaceId) return null;

      const workspaces = await this.storageService.getWorkspaces();
      return workspaces.find(w => w.id === activeWorkspaceId) || null;
    } catch (error) {
      console.error('获取活跃工作区失败:', error);
      return null;
    }
  }

  /**
   * 保存当前工作区状态
   */
  private async saveCurrentWorkspaceState(workspaceId: string): Promise<void> {
    try {
      const currentTabs = await this.tabService.getCurrentTabs();
      const workspaces = await this.storageService.getWorkspaces();
      const workspaceIndex = workspaces.findIndex(w => w.id === workspaceId);
      
      if (workspaceIndex !== -1) {
        workspaces[workspaceIndex].tabs = currentTabs;
        workspaces[workspaceIndex].updatedAt = Date.now();
        await this.storageService.saveWorkspaces(workspaces);
      }
    } catch (error) {
      console.error('保存工作区状态失败:', error);
    }
  }

  /**
   * 恢复工作区标签页
   */
  private async restoreWorkspaceTabs(workspace: Workspace): Promise<void> {
    try {
      for (const tab of workspace.tabs) {
        if (!tab.isSuspended) {
          await this.tabService.createTab(tab.url, workspace.id, {
            pinned: tab.isPinned
          });
        }
      }
    } catch (error) {
      console.error('恢复工作区标签页失败:', error);
    }
  }

  /**
   * 生成唯一工作区ID
   */
  private generateWorkspaceId(): string {
    return `workspace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
