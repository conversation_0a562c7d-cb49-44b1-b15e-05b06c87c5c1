import { Workspace, WorkspaceTab, TabMatcher, WorkspaceFilter } from '@/types';

/**
 * 生成唯一ID
 */
export function generateId(prefix: string = ''): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`;
}

/**
 * 格式化时间
 */
export function formatTime(timestamp: number): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚';
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000));
    return `${minutes}分钟前`;
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000));
    return `${hours}小时前`;
  }

  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    return `${days}天前`;
  }

  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN');
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(timestamp: number): string {
  const rtf = new Intl.RelativeTimeFormat('zh-CN', { numeric: 'auto' });
  const now = Date.now();
  const diff = timestamp - now;

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;
  const year = 365 * day;

  if (Math.abs(diff) < minute) {
    return rtf.format(Math.round(diff / 1000), 'second');
  } else if (Math.abs(diff) < hour) {
    return rtf.format(Math.round(diff / minute), 'minute');
  } else if (Math.abs(diff) < day) {
    return rtf.format(Math.round(diff / hour), 'hour');
  } else if (Math.abs(diff) < week) {
    return rtf.format(Math.round(diff / day), 'day');
  } else if (Math.abs(diff) < month) {
    return rtf.format(Math.round(diff / week), 'week');
  } else if (Math.abs(diff) < year) {
    return rtf.format(Math.round(diff / month), 'month');
  } else {
    return rtf.format(Math.round(diff / year), 'year');
  }
}

/**
 * 从URL提取域名
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    return url;
  }
}

/**
 * 从URL提取网站名称
 */
export function extractSiteName(url: string): string {
  const domain = extractDomain(url);
  const parts = domain.split('.');
  
  if (parts.length >= 2) {
    return parts[parts.length - 2];
  }
  
  return domain;
}

/**
 * 标准化URL（移除查询参数和片段）
 */
export function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
  } catch (error) {
    return url;
  }
}

/**
 * 检查URL是否匹配模式
 */
export function matchesUrlPattern(url: string, pattern: string): boolean {
  try {
    // 支持通配符匹配
    const regexPattern = pattern
      .replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // 转义特殊字符
      .replace(/\\\*/g, '.*') // 恢复通配符
      .replace(/\\\?/g, '.'); // 恢复单字符通配符
    
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(url);
  } catch (error) {
    console.error('URL模式匹配失败:', error);
    return false;
  }
}

/**
 * 创建标签页匹配器
 */
export function createTabMatcher(patterns: string[]): TabMatcher {
  return (url: string) => {
    return patterns.some(pattern => matchesUrlPattern(url, pattern));
  };
}

/**
 * 创建工作区过滤器
 */
export function createWorkspaceFilter(criteria: {
  category?: string;
  hasActiveTabs?: boolean;
  isActive?: boolean;
}): WorkspaceFilter {
  return (workspace: Workspace) => {
    if (criteria.isActive !== undefined && workspace.isActive !== criteria.isActive) {
      return false;
    }
    
    if (criteria.hasActiveTabs !== undefined) {
      const hasActiveTabs = workspace.tabs.some(tab => !tab.isSuspended);
      if (hasActiveTabs !== criteria.hasActiveTabs) {
        return false;
      }
    }
    
    return true;
  };
}

/**
 * 计算工作区统计信息
 */
export function calculateWorkspaceStats(workspace: Workspace) {
  const totalTabs = workspace.tabs.length;
  const activeTabs = workspace.tabs.filter(tab => !tab.isSuspended).length;
  const pinnedTabs = workspace.tabs.filter(tab => tab.isPinned).length;
  const suspendedTabs = workspace.tabs.filter(tab => tab.isSuspended).length;

  return {
    totalTabs,
    activeTabs,
    pinnedTabs,
    suspendedTabs,
    memoryUsage: activeTabs * 50 // 估算内存使用（MB）
  };
}

/**
 * 对工作区进行排序
 */
export function sortWorkspaces(
  workspaces: Workspace[], 
  sortBy: 'name' | 'created' | 'updated' | 'tabs' = 'updated',
  order: 'asc' | 'desc' = 'desc'
): Workspace[] {
  return [...workspaces].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name, 'zh-CN');
        break;
      case 'created':
        comparison = a.createdAt - b.createdAt;
        break;
      case 'updated':
        comparison = a.updatedAt - b.updatedAt;
        break;
      case 'tabs':
        comparison = a.tabs.length - b.tabs.length;
        break;
    }
    
    return order === 'asc' ? comparison : -comparison;
  });
}

/**
 * 搜索工作区
 */
export function searchWorkspaces(workspaces: Workspace[], query: string): Workspace[] {
  if (!query.trim()) {
    return workspaces;
  }
  
  const lowerQuery = query.toLowerCase();
  
  return workspaces.filter(workspace => {
    // 搜索工作区名称和描述
    const nameMatch = workspace.name.toLowerCase().includes(lowerQuery);
    const descMatch = workspace.description?.toLowerCase().includes(lowerQuery);
    
    // 搜索标签页标题和URL
    const tabMatch = workspace.tabs.some(tab => 
      tab.title.toLowerCase().includes(lowerQuery) ||
      tab.url.toLowerCase().includes(lowerQuery)
    );
    
    return nameMatch || descMatch || tabMatch;
  });
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
   * 深拷贝对象
   */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * 获取工作区颜色的CSS类名
 */
export function getWorkspaceColorClass(color: string): string {
  const colorMap: Record<string, string> = {
    '#10b981': 'bg-emerald-500',
    '#f59e0b': 'bg-amber-500',
    '#8b5cf6': 'bg-violet-500',
    '#06b6d4': 'bg-cyan-500',
    '#ef4444': 'bg-red-500',
    '#3b82f6': 'bg-blue-500',
    '#6b7280': 'bg-gray-500'
  };
  
  return colorMap[color] || 'bg-gray-500';
}
