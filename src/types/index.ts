// 工作区类型定义
export interface Workspace {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon?: string;
  tabs: WorkspaceTab[];
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  settings: WorkspaceSettings;
}

// 工作区标签页
export interface WorkspaceTab {
  id: string;
  url: string;
  title: string;
  favIconUrl?: string;
  isPinned: boolean;
  isSuspended: boolean;
  groupId?: string;
  position: number;
  lastAccessed: number;
}

// 工作区设置
export interface WorkspaceSettings {
  autoSuspend: boolean;
  suspendTimeout: number; // 分钟
  autoPinPatterns: string[];
  allowDuplicates: boolean;
  syncEnabled: boolean;
}

// 标签页分组
export interface TabGroup {
  id: string;
  name: string;
  color: string;
  collapsed: boolean;
  workspaceId: string;
}

// 预设工作区配置
export interface PresetWorkspace {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  urls: string[];
  category: WorkspaceCategory;
  settings: Partial<WorkspaceSettings>;
}

// 工作区分类
export type WorkspaceCategory = 'ai-tools' | 'tech-forums' | 'collaboration' | 'development' | 'custom';

// 应用状态
export interface AppState {
  workspaces: Workspace[];
  activeWorkspaceId: string | null;
  settings: AppSettings;
  lastSync: number;
}

// 应用设置
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  shortcuts: Record<string, string>;
  notifications: {
    enabled: boolean;
    suspendNotification: boolean;
    workspaceSwitchNotification: boolean;
  };
  privacy: {
    collectAnalytics: boolean;
    shareUsageData: boolean;
  };
}

// 消息类型（用于background和content script通信）
export interface Message {
  type: MessageType;
  payload?: any;
  workspaceId?: string;
  tabId?: number;
}

export type MessageType =
  | 'CREATE_WORKSPACE'
  | 'SWITCH_WORKSPACE'
  | 'DELETE_WORKSPACE'
  | 'UPDATE_WORKSPACE'
  | 'SUSPEND_TABS'
  | 'RESTORE_TABS'
  | 'PIN_TAB'
  | 'UNPIN_TAB'
  | 'GROUP_TABS'
  | 'GET_WORKSPACES'
  | 'GET_ACTIVE_WORKSPACE'
  | 'SYNC_STATE'
  | 'EXPORT_DATA'
  | 'IMPORT_DATA'
  | 'CREATE_BACKUP'
  | 'GET_BACKUPS'
  | 'RESTORE_BACKUP'
  | 'DELETE_BACKUP'
  | 'GET_STORAGE_USAGE'
  | 'CLEANUP_STORAGE'
  | 'PAGE_INACTIVE'
  | 'PAGE_VISIBLE'
  | 'PAGE_HIDDEN'
  | 'GET_PAGE_INFO'
  | 'CHECK_ACTIVITY';

// 存储键名
export const STORAGE_KEYS = {
  WORKSPACES: 'aitab_workspaces',
  ACTIVE_WORKSPACE: 'aitab_active_workspace',
  APP_SETTINGS: 'aitab_app_settings',
  PRESET_WORKSPACES: 'aitab_preset_workspaces'
} as const;

// 预设工作区数据
export const PRESET_WORKSPACES: PresetWorkspace[] = [
  {
    id: 'ai-main',
    name: 'AI工作主力',
    description: '主要AI工具集合',
    color: '#10b981',
    icon: '🤖',
    category: 'ai-tools',
    urls: [
      'https://chat.openai.com',
      'https://gemini.google.com',
      'https://chat.lobe.run',
      'https://www.perplexity.ai',
      'https://grok.x.ai',
      'https://aistudio.google.com'
    ],
    settings: {
      autoSuspend: true,
      suspendTimeout: 30,
      autoPinPatterns: ['chat.openai.com', 'gemini.google.com']
    }
  },
  {
    id: 'ai-secondary',
    name: 'AI次选工具',
    description: '备用AI工具',
    color: '#8b5cf6',
    icon: '🔧',
    category: 'ai-tools',
    urls: [
      'https://deepask.ai',
      'https://gptfun.ai',
      'https://claude.ai',
      'https://poe.com'
    ],
    settings: {
      autoSuspend: true,
      suspendTimeout: 60
    }
  },
  {
    id: 'tech-forums',
    name: '技术论坛',
    description: '技术交流社区',
    color: '#f59e0b',
    icon: '💬',
    category: 'tech-forums',
    urls: [
      'https://linux.do',
      'https://www.nodeloc.com',
      'https://www.nodeseek.com',
      'https://github.com',
      'https://stackoverflow.com'
    ],
    settings: {
      autoSuspend: false,
      allowDuplicates: true
    }
  },
  {
    id: 'collaboration',
    name: '协作工具',
    description: '团队协作平台',
    color: '#06b6d4',
    icon: '👥',
    category: 'collaboration',
    urls: [
      'https://www.yuque.com',
      'https://www.feishu.cn',
      'https://notion.so',
      'https://slack.com'
    ],
    settings: {
      autoSuspend: false,
      autoPinPatterns: ['yuque.com', 'feishu.cn']
    }
  }
];

// 工具函数类型
export type TabMatcher = (url: string) => boolean;
export type WorkspaceFilter = (workspace: Workspace) => boolean;
