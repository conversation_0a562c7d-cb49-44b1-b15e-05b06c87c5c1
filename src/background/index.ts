import { Message, MessageType } from '@/types';
import { StorageService } from '@/services/storage';
import { WorkspaceService } from '@/services/workspace';
import { TabService } from '@/services/tabs';
import { SyncService } from '@/services/sync';

/**
 * Chrome扩展后台脚本
 * 负责处理扩展的核心逻辑和消息通信
 */
class BackgroundService {
  private storageService: StorageService;
  private workspaceService: WorkspaceService;
  private tabService: TabService;
  private syncService: SyncService;

  constructor() {
    this.storageService = StorageService.getInstance();
    this.workspaceService = WorkspaceService.getInstance();
    this.tabService = TabService.getInstance();
    this.syncService = SyncService.getInstance();
    
    this.initialize();
  }

  /**
   * 初始化后台服务
   */
  private async initialize(): Promise<void> {
    console.log('AI工作台后台服务启动');
    
    // 初始化默认数据
    await this.storageService.initializeDefaults();
    
    // 设置事件监听器
    this.setupEventListeners();
    
    // 设置定时任务
    this.setupPeriodicTasks();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 消息监听
    chrome.runtime.onMessage.addListener(
      (message: Message, sender, sendResponse) => {
        this.handleMessage(message, sender, sendResponse);
        return true; // 保持消息通道开放
      }
    );

    // 标签页事件监听
    chrome.tabs.onCreated.addListener(this.handleTabCreated.bind(this));
    chrome.tabs.onRemoved.addListener(this.handleTabRemoved.bind(this));
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));
    chrome.tabs.onActivated.addListener(this.handleTabActivated.bind(this));

    // 快捷键监听
    chrome.commands.onCommand.addListener(this.handleCommand.bind(this));

    // 扩展安装/更新事件
    chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this));
  }

  /**
   * 处理消息
   */
  private async handleMessage(
    message: Message, 
    sender: chrome.runtime.MessageSender, 
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      switch (message.type) {
        case 'CREATE_WORKSPACE':
          const newWorkspace = await this.workspaceService.createWorkspace(
            message.payload.name,
            message.payload.description,
            message.payload.color,
            message.payload.settings
          );
          sendResponse({ success: true, data: newWorkspace });
          break;

        case 'SWITCH_WORKSPACE':
          await this.workspaceService.switchToWorkspace(message.payload.workspaceId);
          sendResponse({ success: true });
          break;

        case 'DELETE_WORKSPACE':
          await this.workspaceService.deleteWorkspace(message.payload.workspaceId);
          sendResponse({ success: true });
          break;

        case 'UPDATE_WORKSPACE':
          await this.workspaceService.updateWorkspace(
            message.payload.workspaceId,
            message.payload.updates
          );
          sendResponse({ success: true });
          break;

        case 'GET_WORKSPACES':
          const workspaces = await this.storageService.getWorkspaces();
          sendResponse({ success: true, data: workspaces });
          break;

        case 'GET_ACTIVE_WORKSPACE':
          const activeWorkspace = await this.workspaceService.getActiveWorkspace();
          sendResponse({ success: true, data: activeWorkspace });
          break;

        case 'SUSPEND_TABS':
          await this.tabService.suspendInactiveTabs(message.payload?.excludeTabId);
          sendResponse({ success: true });
          break;

        case 'PIN_TAB':
          await this.tabService.pinTab(message.payload.tabId);
          sendResponse({ success: true });
          break;

        case 'UNPIN_TAB':
          await this.tabService.unpinTab(message.payload.tabId);
          sendResponse({ success: true });
          break;

        case 'EXPORT_DATA':
          const exportData = await this.syncService.exportData();
          sendResponse({ success: true, data: exportData });
          break;

        case 'IMPORT_DATA':
          await this.syncService.importData(
            message.payload.jsonData,
            message.payload.options
          );
          sendResponse({ success: true });
          break;

        case 'CREATE_BACKUP':
          await this.syncService.createBackup();
          sendResponse({ success: true });
          break;

        case 'GET_BACKUPS':
          const backups = await this.syncService.getBackups();
          sendResponse({ success: true, data: backups });
          break;

        case 'RESTORE_BACKUP':
          await this.syncService.restoreBackup(message.payload.backupKey);
          sendResponse({ success: true });
          break;

        case 'DELETE_BACKUP':
          await this.syncService.deleteBackup(message.payload.backupKey);
          sendResponse({ success: true });
          break;

        case 'GET_STORAGE_USAGE':
          const usage = await this.syncService.getStorageUsage();
          sendResponse({ success: true, data: usage });
          break;

        case 'CLEANUP_STORAGE':
          await this.syncService.cleanupStorage();
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: '未知消息类型' });
      }
    } catch (error) {
      console.error('处理消息失败:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * 处理标签页创建事件
   */
  private async handleTabCreated(tab: chrome.tabs.Tab): Promise<void> {
    try {
      const activeWorkspace = await this.workspaceService.getActiveWorkspace();
      if (activeWorkspace && tab.url) {
        // 检查是否需要自动固定
        const shouldPin = activeWorkspace.settings.autoPinPatterns.some(pattern =>
          this.matchesPattern(tab.url!, pattern)
        );
        
        if (shouldPin && tab.id) {
          await this.tabService.pinTab(tab.id);
        }
      }
    } catch (error) {
      console.error('处理标签页创建事件失败:', error);
    }
  }

  /**
   * 处理标签页移除事件
   */
  private async handleTabRemoved(tabId: number): Promise<void> {
    // 可以在这里记录标签页关闭事件
    console.log(`标签页 ${tabId} 已关闭`);
  }

  /**
   * 处理标签页更新事件
   */
  private async handleTabUpdated(
    tabId: number, 
    changeInfo: chrome.tabs.TabChangeInfo, 
    tab: chrome.tabs.Tab
  ): Promise<void> {
    try {
      // 如果URL发生变化，检查是否需要自动固定
      if (changeInfo.url && tab.url) {
        const activeWorkspace = await this.workspaceService.getActiveWorkspace();
        if (activeWorkspace) {
          const shouldPin = activeWorkspace.settings.autoPinPatterns.some(pattern =>
            this.matchesPattern(tab.url!, pattern)
          );
          
          if (shouldPin && !tab.pinned) {
            await this.tabService.pinTab(tabId);
          }
        }
      }
    } catch (error) {
      console.error('处理标签页更新事件失败:', error);
    }
  }

  /**
   * 处理标签页激活事件
   */
  private async handleTabActivated(activeInfo: chrome.tabs.TabActiveInfo): Promise<void> {
    // 可以在这里记录标签页访问时间
    console.log(`标签页 ${activeInfo.tabId} 已激活`);
  }

  /**
   * 处理快捷键命令
   */
  private async handleCommand(command: string): Promise<void> {
    try {
      switch (command) {
        case 'switch-workspace':
          // 打开工作区切换界面
          await chrome.action.openPopup();
          break;

        case 'create-workspace':
          // 创建新工作区
          const newWorkspace = await this.workspaceService.createWorkspace(
            `工作区 ${Date.now()}`,
            '通过快捷键创建'
          );
          console.log(`通过快捷键创建工作区: ${newWorkspace.name}`);
          break;

        case 'suspend-tabs':
          // 挂起当前工作区的非活跃标签页
          await this.tabService.suspendInactiveTabs();
          break;
      }
    } catch (error) {
      console.error('处理快捷键命令失败:', error);
    }
  }

  /**
   * 处理扩展安装/更新事件
   */
  private async handleInstalled(details: chrome.runtime.InstalledDetails): Promise<void> {
    try {
      if (details.reason === 'install') {
        console.log('AI工作台首次安装');
        // 可以在这里显示欢迎页面或教程
      } else if (details.reason === 'update') {
        console.log('AI工作台已更新');
        // 可以在这里显示更新日志
      }
    } catch (error) {
      console.error('处理安装事件失败:', error);
    }
  }

  /**
   * 设置定时任务
   */
  private setupPeriodicTasks(): void {
    // 每5分钟检查一次是否需要自动挂起标签页
    setInterval(async () => {
      try {
        const activeWorkspace = await this.workspaceService.getActiveWorkspace();
        if (activeWorkspace?.settings.autoSuspend) {
          const suspendTimeout = activeWorkspace.settings.suspendTimeout * 60 * 1000; // 转换为毫秒
          // 这里可以实现基于时间的自动挂起逻辑
        }
      } catch (error) {
        console.error('定时任务执行失败:', error);
      }
    }, 5 * 60 * 1000); // 5分钟
  }

  /**
   * 检查URL是否匹配模式
   */
  private matchesPattern(url: string, pattern: string): boolean {
    try {
      const regexPattern = pattern
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.');
      
      const regex = new RegExp(regexPattern, 'i');
      return regex.test(url);
    } catch (error) {
      return false;
    }
  }
}

// 启动后台服务
new BackgroundService();
