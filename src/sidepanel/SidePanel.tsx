import React, { useState } from 'react';
import { useWorkspaces } from '@/hooks/useWorkspaces';
import { useTabs } from '@/hooks/useTabs';
import { WorkspaceCard } from '@/components/WorkspaceCard';
import { TabItem } from '@/components/TabItem';
import { Button } from '@/components/Button';
import { 
  Plus, 
  Search, 
  Layers, 
  Zap,
  Settings,
  RefreshCw,
  Pause,
  Play,
  Trash2
} from 'lucide-react';

export function SidePanel() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showWorkspaces, setShowWorkspaces] = useState(true);
  
  const {
    workspaces,
    activeWorkspace,
    loading: workspacesLoading,
    createWorkspace,
    switchWorkspace,
    deleteWorkspace,
    suspendTabs,
    reload: reloadWorkspaces
  } = useWorkspaces();

  const {
    tabs,
    loading: tabsLoading,
    activateTab,
    closeTab,
    pinTab,
    unpinTab,
    suspendTab,
    suspendInactiveTabs,
    closeDuplicates
  } = useTabs();

  const handleCreateWorkspace = async () => {
    const name = prompt('请输入工作区名称:');
    if (name?.trim()) {
      try {
        await createWorkspace(name.trim());
      } catch (error) {
        console.error('创建工作区失败:', error);
      }
    }
  };

  const handleSwitchWorkspace = async (workspaceId: string) => {
    try {
      await switchWorkspace(workspaceId);
    } catch (error) {
      console.error('切换工作区失败:', error);
    }
  };

  const handleSuspendAll = async () => {
    try {
      await suspendInactiveTabs();
    } catch (error) {
      console.error('挂起标签页失败:', error);
    }
  };

  const filteredWorkspaces = workspaces.filter(workspace =>
    workspace.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredTabs = tabs.filter(tab =>
    tab.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tab.url?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const activeTabs = tabs.filter(tab => !tab.discarded);
  const suspendedTabs = tabs.filter(tab => tab.discarded);
  const pinnedTabs = tabs.filter(tab => tab.pinned);

  return (
    <div className="w-full h-full bg-white flex flex-col">
      {/* 头部 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-semibold text-gray-900">AI工作台</h1>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              icon={<RefreshCw className="w-4 h-4" />}
              onClick={reloadWorkspaces}
              title="刷新"
            />
            <Button
              size="sm"
              variant="ghost"
              icon={<Settings className="w-4 h-4" />}
              title="设置"
            />
          </div>
        </div>

        {/* 搜索框 */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索..."
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* 视图切换 */}
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant={showWorkspaces ? 'primary' : 'ghost'}
            icon={<Layers className="w-4 h-4" />}
            onClick={() => setShowWorkspaces(true)}
            className="flex-1"
          >
            工作区
          </Button>
          <Button
            size="sm"
            variant={!showWorkspaces ? 'primary' : 'ghost'}
            icon={<Zap className="w-4 h-4" />}
            onClick={() => setShowWorkspaces(false)}
            className="flex-1"
          >
            标签页
          </Button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto">
        {showWorkspaces ? (
          /* 工作区视图 */
          <div className="p-4">
            {/* 当前活跃工作区 */}
            {activeWorkspace && (
              <div className="mb-6">
                <h2 className="text-sm font-medium text-gray-900 mb-3">当前工作区</h2>
                <WorkspaceCard
                  workspace={activeWorkspace}
                  isActive={true}
                  onSuspend={suspendTabs}
                />
              </div>
            )}

            {/* 工作区列表 */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-sm font-medium text-gray-900">
                  所有工作区 ({filteredWorkspaces.length})
                </h2>
                <Button
                  size="sm"
                  icon={<Plus className="w-4 h-4" />}
                  onClick={handleCreateWorkspace}
                >
                  新建
                </Button>
              </div>

              {workspacesLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="loading-spinner w-6 h-6" />
                </div>
              ) : filteredWorkspaces.length > 0 ? (
                <div className="space-y-2">
                  {filteredWorkspaces.map((workspace) => (
                    <WorkspaceCard
                      key={workspace.id}
                      workspace={workspace}
                      isActive={workspace.id === activeWorkspace?.id}
                      onSwitch={handleSwitchWorkspace}
                      onDelete={deleteWorkspace}
                      onSuspend={suspendTabs}
                    />
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <Layers className="w-12 h-12 text-gray-300 mb-4" />
                  <p className="text-sm text-gray-500">
                    {searchQuery ? '没有找到匹配的工作区' : '还没有工作区'}
                  </p>
                </div>
              )}
            </div>
          </div>
        ) : (
          /* 标签页视图 */
          <div className="p-4">
            {/* 标签页统计 */}
            <div className="grid grid-cols-3 gap-2 mb-4">
              <div className="text-center p-2 bg-blue-50 rounded">
                <div className="text-lg font-semibold text-blue-600">{activeTabs.length}</div>
                <div className="text-xs text-blue-600">活跃</div>
              </div>
              <div className="text-center p-2 bg-yellow-50 rounded">
                <div className="text-lg font-semibold text-yellow-600">{pinnedTabs.length}</div>
                <div className="text-xs text-yellow-600">固定</div>
              </div>
              <div className="text-center p-2 bg-gray-50 rounded">
                <div className="text-lg font-semibold text-gray-600">{suspendedTabs.length}</div>
                <div className="text-xs text-gray-600">挂起</div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="flex space-x-2 mb-4">
              <Button
                size="sm"
                variant="secondary"
                icon={<Pause className="w-4 h-4" />}
                onClick={handleSuspendAll}
                className="flex-1"
              >
                挂起非活跃
              </Button>
              <Button
                size="sm"
                variant="secondary"
                icon={<Trash2 className="w-4 h-4" />}
                onClick={closeDuplicates}
                className="flex-1"
              >
                去重
              </Button>
            </div>

            {/* 标签页列表 */}
            <div>
              <h2 className="text-sm font-medium text-gray-900 mb-3">
                标签页 ({filteredTabs.length})
              </h2>

              {tabsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="loading-spinner w-6 h-6" />
                </div>
              ) : filteredTabs.length > 0 ? (
                <div className="space-y-2">
                  {/* 固定标签页 */}
                  {pinnedTabs.length > 0 && (
                    <div className="mb-4">
                      <h3 className="text-xs font-medium text-gray-500 mb-2">固定标签页</h3>
                      {pinnedTabs.filter(tab => 
                        !searchQuery || 
                        tab.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        tab.url?.toLowerCase().includes(searchQuery.toLowerCase())
                      ).map((tab) => (
                        <TabItem
                          key={tab.id}
                          tab={tab}
                          isActive={tab.active}
                          isPinned={tab.pinned}
                          isSuspended={tab.discarded}
                          onActivate={activateTab}
                          onClose={closeTab}
                          onPin={pinTab}
                          onUnpin={unpinTab}
                          onSuspend={suspendTab}
                        />
                      ))}
                    </div>
                  )}

                  {/* 普通标签页 */}
                  {filteredTabs.filter(tab => !tab.pinned).map((tab) => (
                    <TabItem
                      key={tab.id}
                      tab={tab}
                      isActive={tab.active}
                      isPinned={tab.pinned}
                      isSuspended={tab.discarded}
                      onActivate={activateTab}
                      onClose={closeTab}
                      onPin={pinTab}
                      onUnpin={unpinTab}
                      onSuspend={suspendTab}
                    />
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <Zap className="w-12 h-12 text-gray-300 mb-4" />
                  <p className="text-sm text-gray-500">
                    {searchQuery ? '没有找到匹配的标签页' : '没有打开的标签页'}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
