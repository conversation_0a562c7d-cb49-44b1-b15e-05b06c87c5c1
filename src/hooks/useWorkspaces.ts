import { useState, useEffect, useCallback } from 'react';
import { Workspace, Message } from '@/types';

/**
 * 工作区管理Hook
 */
export function useWorkspaces() {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [activeWorkspace, setActiveWorkspace] = useState<Workspace | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * 发送消息到后台脚本
   */
  const sendMessage = useCallback(async (message: Message): Promise<any> => {
    try {
      const response = await chrome.runtime.sendMessage(message);
      if (!response.success) {
        throw new Error(response.error || '操作失败');
      }
      return response.data;
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }, []);

  /**
   * 加载工作区列表
   */
  const loadWorkspaces = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [workspacesData, activeWorkspaceData] = await Promise.all([
        sendMessage({ type: 'GET_WORKSPACES' }),
        sendMessage({ type: 'GET_ACTIVE_WORKSPACE' })
      ]);
      
      setWorkspaces(workspacesData || []);
      setActiveWorkspace(activeWorkspaceData);
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载工作区失败');
    } finally {
      setLoading(false);
    }
  }, [sendMessage]);

  /**
   * 创建新工作区
   */
  const createWorkspace = useCallback(async (
    name: string,
    description?: string,
    color?: string,
    settings?: any
  ): Promise<Workspace> => {
    try {
      const newWorkspace = await sendMessage({
        type: 'CREATE_WORKSPACE',
        payload: { name, description, color, settings }
      });
      
      await loadWorkspaces(); // 重新加载列表
      return newWorkspace;
    } catch (error) {
      setError(error instanceof Error ? error.message : '创建工作区失败');
      throw error;
    }
  }, [sendMessage, loadWorkspaces]);

  /**
   * 切换工作区
   */
  const switchWorkspace = useCallback(async (workspaceId: string): Promise<void> => {
    try {
      await sendMessage({
        type: 'SWITCH_WORKSPACE',
        payload: { workspaceId }
      });
      
      await loadWorkspaces(); // 重新加载以更新状态
    } catch (error) {
      setError(error instanceof Error ? error.message : '切换工作区失败');
      throw error;
    }
  }, [sendMessage, loadWorkspaces]);

  /**
   * 删除工作区
   */
  const deleteWorkspace = useCallback(async (workspaceId: string): Promise<void> => {
    try {
      await sendMessage({
        type: 'DELETE_WORKSPACE',
        payload: { workspaceId }
      });
      
      await loadWorkspaces(); // 重新加载列表
    } catch (error) {
      setError(error instanceof Error ? error.message : '删除工作区失败');
      throw error;
    }
  }, [sendMessage, loadWorkspaces]);

  /**
   * 更新工作区
   */
  const updateWorkspace = useCallback(async (
    workspaceId: string,
    updates: Partial<Pick<Workspace, 'name' | 'description' | 'color' | 'settings'>>
  ): Promise<void> => {
    try {
      await sendMessage({
        type: 'UPDATE_WORKSPACE',
        payload: { workspaceId, updates }
      });
      
      await loadWorkspaces(); // 重新加载列表
    } catch (error) {
      setError(error instanceof Error ? error.message : '更新工作区失败');
      throw error;
    }
  }, [sendMessage, loadWorkspaces]);

  /**
   * 挂起标签页
   */
  const suspendTabs = useCallback(async (excludeTabId?: number): Promise<void> => {
    try {
      await sendMessage({
        type: 'SUSPEND_TABS',
        payload: { excludeTabId }
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : '挂起标签页失败');
      throw error;
    }
  }, [sendMessage]);

  /**
   * 固定标签页
   */
  const pinTab = useCallback(async (tabId: number): Promise<void> => {
    try {
      await sendMessage({
        type: 'PIN_TAB',
        payload: { tabId }
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : '固定标签页失败');
      throw error;
    }
  }, [sendMessage]);

  /**
   * 取消固定标签页
   */
  const unpinTab = useCallback(async (tabId: number): Promise<void> => {
    try {
      await sendMessage({
        type: 'UNPIN_TAB',
        payload: { tabId }
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : '取消固定标签页失败');
      throw error;
    }
  }, [sendMessage]);

  // 初始化加载
  useEffect(() => {
    loadWorkspaces();
  }, [loadWorkspaces]);

  return {
    workspaces,
    activeWorkspace,
    loading,
    error,
    createWorkspace,
    switchWorkspace,
    deleteWorkspace,
    updateWorkspace,
    suspendTabs,
    pinTab,
    unpinTab,
    reload: loadWorkspaces
  };
}
