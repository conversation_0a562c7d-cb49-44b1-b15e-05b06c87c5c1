import { useState, useCallback } from 'react';

/**
 * 数据同步管理Hook
 */
export function useSync() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 导出数据
   */
  const exportData = useCallback(async (): Promise<string | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await chrome.runtime.sendMessage({
        type: 'EXPORT_DATA'
      });
      
      if (!response.success) {
        throw new Error(response.error || '导出数据失败');
      }
      
      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '导出数据失败';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 导入数据
   */
  const importData = useCallback(async (
    jsonData: string,
    options?: {
      mergeWorkspaces?: boolean;
      mergePresets?: boolean;
      overwriteSettings?: boolean;
    }
  ): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await chrome.runtime.sendMessage({
        type: 'IMPORT_DATA',
        payload: { jsonData, options }
      });
      
      if (!response.success) {
        throw new Error(response.error || '导入数据失败');
      }
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '导入数据失败';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 创建备份
   */
  const createBackup = useCallback(async (): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await chrome.runtime.sendMessage({
        type: 'CREATE_BACKUP'
      });
      
      if (!response.success) {
        throw new Error(response.error || '创建备份失败');
      }
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建备份失败';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 获取备份列表
   */
  const getBackups = useCallback(async (): Promise<Array<{
    key: string;
    timestamp: number;
    size: number;
  }> | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await chrome.runtime.sendMessage({
        type: 'GET_BACKUPS'
      });
      
      if (!response.success) {
        throw new Error(response.error || '获取备份列表失败');
      }
      
      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取备份列表失败';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 恢复备份
   */
  const restoreBackup = useCallback(async (backupKey: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await chrome.runtime.sendMessage({
        type: 'RESTORE_BACKUP',
        payload: { backupKey }
      });
      
      if (!response.success) {
        throw new Error(response.error || '恢复备份失败');
      }
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '恢复备份失败';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 删除备份
   */
  const deleteBackup = useCallback(async (backupKey: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await chrome.runtime.sendMessage({
        type: 'DELETE_BACKUP',
        payload: { backupKey }
      });
      
      if (!response.success) {
        throw new Error(response.error || '删除备份失败');
      }
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除备份失败';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 获取存储使用情况
   */
  const getStorageUsage = useCallback(async (): Promise<{
    used: number;
    total: number;
    percentage: number;
    breakdown: Record<string, number>;
  } | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await chrome.runtime.sendMessage({
        type: 'GET_STORAGE_USAGE'
      });
      
      if (!response.success) {
        throw new Error(response.error || '获取存储使用情况失败');
      }
      
      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取存储使用情况失败';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 清理存储空间
   */
  const cleanupStorage = useCallback(async (): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await chrome.runtime.sendMessage({
        type: 'CLEANUP_STORAGE'
      });
      
      if (!response.success) {
        throw new Error(response.error || '清理存储空间失败');
      }
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '清理存储空间失败';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 下载数据文件
   */
  const downloadData = useCallback(async (filename?: string): Promise<void> => {
    try {
      const data = await exportData();
      if (!data) return;
      
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || `aitab-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '下载数据失败';
      setError(errorMessage);
    }
  }, [exportData]);

  /**
   * 从文件导入数据
   */
  const importFromFile = useCallback(async (
    file: File,
    options?: {
      mergeWorkspaces?: boolean;
      mergePresets?: boolean;
      overwriteSettings?: boolean;
    }
  ): Promise<boolean> => {
    try {
      const text = await file.text();
      return await importData(text, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '从文件导入数据失败';
      setError(errorMessage);
      return false;
    }
  }, [importData]);

  return {
    loading,
    error,
    exportData,
    importData,
    createBackup,
    getBackups,
    restoreBackup,
    deleteBackup,
    getStorageUsage,
    cleanupStorage,
    downloadData,
    importFromFile
  };
}
