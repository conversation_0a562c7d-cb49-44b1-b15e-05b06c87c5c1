import { useState, useEffect, useCallback } from 'react';
import { WorkspaceTab } from '@/types';

/**
 * 标签页管理Hook
 */
export function useTabs() {
  const [tabs, setTabs] = useState<chrome.tabs.Tab[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * 获取当前窗口的所有标签页
   */
  const loadTabs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      setTabs(currentTabs);
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载标签页失败');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 创建新标签页
   */
  const createTab = useCallback(async (
    url: string,
    options?: { pinned?: boolean; active?: boolean }
  ): Promise<chrome.tabs.Tab | null> => {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned: options?.pinned || false,
        active: options?.active !== false // 默认激活
      });
      
      await loadTabs(); // 重新加载标签页列表
      return tab;
    } catch (error) {
      setError(error instanceof Error ? error.message : '创建标签页失败');
      return null;
    }
  }, [loadTabs]);

  /**
   * 关闭标签页
   */
  const closeTab = useCallback(async (tabId: number): Promise<void> => {
    try {
      await chrome.tabs.remove(tabId);
      await loadTabs(); // 重新加载标签页列表
    } catch (error) {
      setError(error instanceof Error ? error.message : '关闭标签页失败');
    }
  }, [loadTabs]);

  /**
   * 关闭多个标签页
   */
  const closeTabs = useCallback(async (tabIds: number[]): Promise<void> => {
    try {
      await chrome.tabs.remove(tabIds);
      await loadTabs(); // 重新加载标签页列表
    } catch (error) {
      setError(error instanceof Error ? error.message : '批量关闭标签页失败');
    }
  }, [loadTabs]);

  /**
   * 激活标签页
   */
  const activateTab = useCallback(async (tabId: number): Promise<void> => {
    try {
      await chrome.tabs.update(tabId, { active: true });
    } catch (error) {
      setError(error instanceof Error ? error.message : '激活标签页失败');
    }
  }, []);

  /**
   * 固定标签页
   */
  const pinTab = useCallback(async (tabId: number): Promise<void> => {
    try {
      await chrome.tabs.update(tabId, { pinned: true });
      await loadTabs(); // 重新加载以更新状态
    } catch (error) {
      setError(error instanceof Error ? error.message : '固定标签页失败');
    }
  }, [loadTabs]);

  /**
   * 取消固定标签页
   */
  const unpinTab = useCallback(async (tabId: number): Promise<void> => {
    try {
      await chrome.tabs.update(tabId, { pinned: false });
      await loadTabs(); // 重新加载以更新状态
    } catch (error) {
      setError(error instanceof Error ? error.message : '取消固定标签页失败');
    }
  }, [loadTabs]);

  /**
   * 挂起标签页
   */
  const suspendTab = useCallback(async (tabId: number): Promise<void> => {
    try {
      await chrome.tabs.discard(tabId);
      await loadTabs(); // 重新加载以更新状态
    } catch (error) {
      setError(error instanceof Error ? error.message : '挂起标签页失败');
    }
  }, [loadTabs]);

  /**
   * 批量挂起标签页
   */
  const suspendTabs = useCallback(async (tabIds: number[]): Promise<void> => {
    try {
      for (const tabId of tabIds) {
        await chrome.tabs.discard(tabId);
      }
      await loadTabs(); // 重新加载以更新状态
    } catch (error) {
      setError(error instanceof Error ? error.message : '批量挂起标签页失败');
    }
  }, [loadTabs]);

  /**
   * 挂起非活跃标签页
   */
  const suspendInactiveTabs = useCallback(async (excludeTabId?: number): Promise<void> => {
    try {
      const inactiveTabs = tabs.filter(tab => 
        tab.id !== excludeTabId && 
        !tab.active && 
        !tab.pinned && 
        !tab.discarded
      );
      
      const tabIds = inactiveTabs.map(tab => tab.id).filter(id => id !== undefined) as number[];
      
      if (tabIds.length > 0) {
        await suspendTabs(tabIds);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '挂起非活跃标签页失败');
    }
  }, [tabs, suspendTabs]);

  /**
   * 检测重复标签页
   */
  const detectDuplicates = useCallback((): chrome.tabs.Tab[][] => {
    const urlGroups = new Map<string, chrome.tabs.Tab[]>();
    
    // 按URL分组
    for (const tab of tabs) {
      if (tab.url) {
        const normalizedUrl = normalizeUrl(tab.url);
        if (!urlGroups.has(normalizedUrl)) {
          urlGroups.set(normalizedUrl, []);
        }
        urlGroups.get(normalizedUrl)!.push(tab);
      }
    }
    
    // 返回有重复的分组
    return Array.from(urlGroups.values()).filter(group => group.length > 1);
  }, [tabs]);

  /**
   * 关闭重复标签页
   */
  const closeDuplicates = useCallback(async (): Promise<void> => {
    try {
      const duplicateGroups = detectDuplicates();
      
      for (const group of duplicateGroups) {
        // 按最后访问时间排序，保留最新的
        const sortedTabs = group.sort((a, b) => 
          (b.lastAccessed || 0) - (a.lastAccessed || 0)
        );
        
        // 关闭除第一个外的所有标签页
        const tabsToClose = sortedTabs.slice(1);
        const tabIds = tabsToClose.map(tab => tab.id).filter(id => id !== undefined) as number[];
        
        if (tabIds.length > 0) {
          await closeTabs(tabIds);
        }
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '关闭重复标签页失败');
    }
  }, [detectDuplicates, closeTabs]);

  /**
   * 创建标签页分组
   */
  const createTabGroup = useCallback(async (
    tabIds: number[],
    groupName: string,
    color: chrome.tabGroups.ColorEnum = 'blue'
  ): Promise<number | null> => {
    try {
      const groupId = await chrome.tabs.group({ tabIds });
      await chrome.tabGroups.update(groupId, {
        title: groupName,
        color: color
      });
      
      await loadTabs(); // 重新加载以更新状态
      return groupId;
    } catch (error) {
      setError(error instanceof Error ? error.message : '创建标签页分组失败');
      return null;
    }
  }, [loadTabs]);

  // 标准化URL（移除查询参数和片段）
  const normalizeUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
    } catch (error) {
      return url;
    }
  };

  // 监听标签页变化
  useEffect(() => {
    const handleTabCreated = () => loadTabs();
    const handleTabRemoved = () => loadTabs();
    const handleTabUpdated = () => loadTabs();

    chrome.tabs.onCreated.addListener(handleTabCreated);
    chrome.tabs.onRemoved.addListener(handleTabRemoved);
    chrome.tabs.onUpdated.addListener(handleTabUpdated);

    return () => {
      chrome.tabs.onCreated.removeListener(handleTabCreated);
      chrome.tabs.onRemoved.removeListener(handleTabRemoved);
      chrome.tabs.onUpdated.removeListener(handleTabUpdated);
    };
  }, [loadTabs]);

  // 初始化加载
  useEffect(() => {
    loadTabs();
  }, [loadTabs]);

  return {
    tabs,
    loading,
    error,
    createTab,
    closeTab,
    closeTabs,
    activateTab,
    pinTab,
    unpinTab,
    suspendTab,
    suspendTabs,
    suspendInactiveTabs,
    detectDuplicates,
    closeDuplicates,
    createTabGroup,
    reload: loadTabs
  };
}
