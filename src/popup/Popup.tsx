import React, { useState } from 'react';
import { useWorkspaces } from '@/hooks/useWorkspaces';
import { usePresets } from '@/hooks/usePresets';
import { useTabs } from '@/hooks/useTabs';
import { WorkspaceCard } from '@/components/WorkspaceCard';
import { PresetCard } from '@/components/PresetCard';
import { TabItem } from '@/components/TabItem';
import { Button } from '@/components/Button';
import { 
  Plus, 
  Settings, 
  Search, 
  Layers, 
  Bookmark, 
  Zap,
  RefreshCw,
  X
} from 'lucide-react';

type TabType = 'workspaces' | 'presets' | 'tabs' | 'settings';

export function Popup() {
  const [activeTab, setActiveTab] = useState<TabType>('workspaces');
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  
  const {
    workspaces,
    activeWorkspace,
    loading: workspacesLoading,
    error: workspacesError,
    createWorkspace,
    switchWorkspace,
    deleteWorkspace,
    suspendTabs,
    reload: reloadWorkspaces
  } = useWorkspaces();

  const {
    presets,
    loading: presetsLoading,
    createWorkspaceFromPreset,
    createAIToolsSuite,
    createCompleteEnvironment
  } = usePresets();

  const {
    tabs,
    loading: tabsLoading,
    activateTab,
    closeTab,
    pinTab,
    unpinTab,
    suspendTab,
    closeDuplicates
  } = useTabs();

  const handleCreateWorkspace = async (name: string, description?: string) => {
    try {
      await createWorkspace(name, description);
      setShowCreateForm(false);
    } catch (error) {
      console.error('创建工作区失败:', error);
    }
  };

  const handleSwitchWorkspace = async (workspaceId: string) => {
    try {
      await switchWorkspace(workspaceId);
      // 关闭popup
      window.close();
    } catch (error) {
      console.error('切换工作区失败:', error);
    }
  };

  const handleCreateFromPreset = async (presetId: string) => {
    try {
      await createWorkspaceFromPreset(presetId);
      setActiveTab('workspaces');
    } catch (error) {
      console.error('从预设创建工作区失败:', error);
    }
  };

  const filteredWorkspaces = workspaces.filter(workspace =>
    workspace.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    workspace.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredPresets = presets.filter(preset =>
    preset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    preset.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredTabs = tabs.filter(tab =>
    tab.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tab.url?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="w-full h-full bg-white flex flex-col">
      {/* 头部 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-semibold text-gray-900">AI工作台</h1>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="ghost"
              icon={<RefreshCw className="w-4 h-4" />}
              onClick={reloadWorkspaces}
              title="刷新"
            />
            <Button
              size="sm"
              variant="ghost"
              icon={<Settings className="w-4 h-4" />}
              onClick={() => setActiveTab('settings')}
              title="设置"
            />
          </div>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索工作区、预设或标签页..."
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="flex-shrink-0 flex border-b border-gray-200">
        <button
          className={`flex-1 px-4 py-3 text-sm font-medium ${
            activeTab === 'workspaces'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('workspaces')}
        >
          <Layers className="w-4 h-4 mr-2 inline" />
          工作区
        </button>
        <button
          className={`flex-1 px-4 py-3 text-sm font-medium ${
            activeTab === 'presets'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('presets')}
        >
          <Bookmark className="w-4 h-4 mr-2 inline" />
          预设
        </button>
        <button
          className={`flex-1 px-4 py-3 text-sm font-medium ${
            activeTab === 'tabs'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('tabs')}
        >
          <Zap className="w-4 h-4 mr-2 inline" />
          标签页
        </button>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto">
        {/* 工作区列表 */}
        {activeTab === 'workspaces' && (
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-gray-900">
                我的工作区 ({filteredWorkspaces.length})
              </h2>
              <Button
                size="sm"
                icon={<Plus className="w-4 h-4" />}
                onClick={() => setShowCreateForm(true)}
              >
                新建
              </Button>
            </div>

            {workspacesLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="loading-spinner w-6 h-6" />
              </div>
            ) : filteredWorkspaces.length > 0 ? (
              <div className="space-y-2">
                {filteredWorkspaces.map((workspace) => (
                  <WorkspaceCard
                    key={workspace.id}
                    workspace={workspace}
                    isActive={workspace.id === activeWorkspace?.id}
                    onSwitch={handleSwitchWorkspace}
                    onDelete={deleteWorkspace}
                    onSuspend={suspendTabs}
                  />
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <Layers className="w-12 h-12 text-gray-300 mb-4" />
                <p className="text-sm text-gray-500">
                  {searchQuery ? '没有找到匹配的工作区' : '还没有工作区'}
                </p>
                {!searchQuery && (
                  <Button
                    size="sm"
                    className="mt-2"
                    onClick={() => setShowCreateForm(true)}
                  >
                    创建第一个工作区
                  </Button>
                )}
              </div>
            )}
          </div>
        )}

        {/* 预设工作区 */}
        {activeTab === 'presets' && (
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-gray-900">
                预设工作区 ({filteredPresets.length})
              </h2>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={createAIToolsSuite}
                >
                  AI套件
                </Button>
                <Button
                  size="sm"
                  onClick={createCompleteEnvironment}
                >
                  完整环境
                </Button>
              </div>
            </div>

            {presetsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="loading-spinner w-6 h-6" />
              </div>
            ) : filteredPresets.length > 0 ? (
              <div className="grid grid-cols-1 gap-3">
                {filteredPresets.map((preset) => (
                  <PresetCard
                    key={preset.id}
                    preset={preset}
                    onCreateWorkspace={handleCreateFromPreset}
                  />
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <Bookmark className="w-12 h-12 text-gray-300 mb-4" />
                <p className="text-sm text-gray-500">
                  {searchQuery ? '没有找到匹配的预设' : '没有可用的预设'}
                </p>
              </div>
            )}
          </div>
        )}

        {/* 标签页管理 */}
        {activeTab === 'tabs' && (
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-gray-900">
                当前标签页 ({filteredTabs.length})
              </h2>
              <Button
                size="sm"
                variant="secondary"
                onClick={closeDuplicates}
              >
                去重
              </Button>
            </div>

            {tabsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="loading-spinner w-6 h-6" />
              </div>
            ) : filteredTabs.length > 0 ? (
              <div className="space-y-2">
                {filteredTabs.map((tab) => (
                  <TabItem
                    key={tab.id}
                    tab={tab}
                    isActive={tab.active}
                    isPinned={tab.pinned}
                    isSuspended={tab.discarded}
                    onActivate={activateTab}
                    onClose={closeTab}
                    onPin={pinTab}
                    onUnpin={unpinTab}
                    onSuspend={suspendTab}
                  />
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <Zap className="w-12 h-12 text-gray-300 mb-4" />
                <p className="text-sm text-gray-500">
                  {searchQuery ? '没有找到匹配的标签页' : '没有打开的标签页'}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 创建工作区表单 */}
      {showCreateForm && (
        <CreateWorkspaceForm
          onSubmit={handleCreateWorkspace}
          onCancel={() => setShowCreateForm(false)}
        />
      )}
    </div>
  );
}

// 创建工作区表单组件
function CreateWorkspaceForm({
  onSubmit,
  onCancel
}: {
  onSubmit: (name: string, description?: string) => void;
  onCancel: () => void;
}) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onSubmit(name.trim(), description.trim() || undefined);
    }
  };

  return (
    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg p-6 w-full max-w-sm">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">创建新工作区</h3>
          <Button
            size="sm"
            variant="ghost"
            icon={<X className="w-4 h-4" />}
            onClick={onCancel}
          />
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              工作区名称
            </label>
            <input
              type="text"
              className="input"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入工作区名称"
              autoFocus
              required
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述（可选）
            </label>
            <textarea
              className="input resize-none"
              rows={3}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="输入工作区描述"
            />
          </div>

          <div className="flex space-x-3">
            <Button
              type="button"
              variant="secondary"
              className="flex-1"
              onClick={onCancel}
            >
              取消
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={!name.trim()}
            >
              创建
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
