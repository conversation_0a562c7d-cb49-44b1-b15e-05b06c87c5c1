import { Message } from '@/types';

/**
 * 内容脚本
 * 在网页中注入，用于监控页面活动和提供页面级功能
 */
class ContentScript {
  private lastActivityTime: number = Date.now();
  private activityCheckInterval: number | null = null;

  constructor() {
    this.initialize();
  }

  /**
   * 初始化内容脚本
   */
  private initialize(): void {
    console.log('AI工作台内容脚本已加载:', window.location.href);
    
    // 监听页面活动
    this.setupActivityMonitoring();
    
    // 监听来自后台脚本的消息
    this.setupMessageListener();
    
    // 设置页面可见性变化监听
    this.setupVisibilityListener();
  }

  /**
   * 设置页面活动监控
   */
  private setupActivityMonitoring(): void {
    // 监听用户交互事件
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, () => {
        this.lastActivityTime = Date.now();
      }, { passive: true });
    });

    // 定期检查页面活动状态
    this.activityCheckInterval = window.setInterval(() => {
      this.checkActivityStatus();
    }, 60000); // 每分钟检查一次
  }

  /**
   * 设置消息监听器
   */
  private setupMessageListener(): void {
    chrome.runtime.onMessage.addListener(
      (message: Message, sender, sendResponse) => {
        this.handleMessage(message, sender, sendResponse);
        return true;
      }
    );
  }

  /**
   * 设置页面可见性监听
   */
  private setupVisibilityListener(): void {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.lastActivityTime = Date.now();
        this.notifyPageVisible();
      } else {
        this.notifyPageHidden();
      }
    });
  }

  /**
   * 处理来自后台脚本的消息
   */
  private handleMessage(
    message: Message, 
    sender: chrome.runtime.MessageSender, 
    sendResponse: (response?: any) => void
  ): void {
    switch (message.type) {
      case 'GET_PAGE_INFO':
        sendResponse({
          url: window.location.href,
          title: document.title,
          lastActivity: this.lastActivityTime,
          isVisible: document.visibilityState === 'visible'
        });
        break;

      case 'CHECK_ACTIVITY':
        const isActive = this.isPageActive();
        sendResponse({ isActive, lastActivity: this.lastActivityTime });
        break;

      default:
        sendResponse({ success: false, error: '未知消息类型' });
    }
  }

  /**
   * 检查页面活动状态
   */
  private checkActivityStatus(): void {
    const now = Date.now();
    const inactiveTime = now - this.lastActivityTime;
    const isInactive = inactiveTime > 5 * 60 * 1000; // 5分钟无活动

    if (isInactive && document.visibilityState === 'hidden') {
      // 通知后台脚本页面可能需要挂起
      this.sendMessage({
        type: 'PAGE_INACTIVE',
        payload: {
          url: window.location.href,
          inactiveTime: inactiveTime
        }
      });
    }
  }

  /**
   * 检查页面是否活跃
   */
  private isPageActive(): boolean {
    const now = Date.now();
    const inactiveTime = now - this.lastActivityTime;
    return inactiveTime < 5 * 60 * 1000 && document.visibilityState === 'visible';
  }

  /**
   * 通知页面变为可见
   */
  private notifyPageVisible(): void {
    this.sendMessage({
      type: 'PAGE_VISIBLE',
      payload: {
        url: window.location.href,
        title: document.title
      }
    });
  }

  /**
   * 通知页面变为隐藏
   */
  private notifyPageHidden(): void {
    this.sendMessage({
      type: 'PAGE_HIDDEN',
      payload: {
        url: window.location.href,
        lastActivity: this.lastActivityTime
      }
    });
  }

  /**
   * 向后台脚本发送消息
   */
  private sendMessage(message: Message): void {
    chrome.runtime.sendMessage(message).catch(error => {
      console.error('发送消息到后台脚本失败:', error);
    });
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.activityCheckInterval) {
      clearInterval(this.activityCheckInterval);
      this.activityCheckInterval = null;
    }
  }
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  // 清理逻辑会在ContentScript实例销毁时自动执行
});

// 启动内容脚本
new ContentScript();
