@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置 */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html, body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
      'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.8);
  }
}

/* 组件样式 */
@layer components {
  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  .btn-sm {
    @apply px-2 py-1 text-xs;
  }
  
  .btn-lg {
    @apply px-4 py-3 text-base;
  }
  
  /* 输入框样式 */
  .input {
    @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .card-header {
    @apply px-4 py-3 border-b border-gray-200;
  }
  
  .card-body {
    @apply p-4;
  }
  
  .card-footer {
    @apply px-4 py-3 border-t border-gray-200 bg-gray-50;
  }
  
  /* 工作区颜色指示器 */
  .workspace-indicator {
    @apply w-3 h-3 rounded-full flex-shrink-0;
  }
  
  /* 标签页项目样式 */
  .tab-item {
    @apply flex items-center p-2 rounded-md hover:bg-gray-100 transition-colors duration-150 cursor-pointer;
  }
  
  .tab-item.active {
    @apply bg-blue-50 border-blue-200;
  }
  
  .tab-item.pinned {
    @apply bg-yellow-50 border-yellow-200;
  }
  
  .tab-item.suspended {
    @apply opacity-60 bg-gray-50;
  }
  
  /* 工作区项目样式 */
  .workspace-item {
    @apply flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-150 cursor-pointer border border-transparent;
  }
  
  .workspace-item.active {
    @apply bg-blue-50 border-blue-200;
  }
  
  /* 预设工作区样式 */
  .preset-item {
    @apply p-4 rounded-lg border-2 border-dashed border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 cursor-pointer;
  }
  
  .preset-item.ai-tools {
    @apply border-emerald-200 hover:border-emerald-300 hover:bg-emerald-50;
  }
  
  .preset-item.tech-forums {
    @apply border-amber-200 hover:border-amber-300 hover:bg-amber-50;
  }
  
  .preset-item.collaboration {
    @apply border-violet-200 hover:border-violet-300 hover:bg-violet-50;
  }
  
  /* 加载状态 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }
  
  /* 空状态 */
  .empty-state {
    @apply flex flex-col items-center justify-center p-8 text-center text-gray-500;
  }
  
  /* 通知样式 */
  .notification {
    @apply fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 max-w-sm;
  }
  
  .notification.success {
    @apply bg-green-100 border border-green-200 text-green-800;
  }
  
  .notification.error {
    @apply bg-red-100 border border-red-200 text-red-800;
  }
  
  .notification.warning {
    @apply bg-yellow-100 border border-yellow-200 text-yellow-800;
  }
  
  .notification.info {
    @apply bg-blue-100 border border-blue-200 text-blue-800;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }
  
  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 动画 */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-pulse-soft {
    animation: pulseSoft 2s infinite;
  }
  
  /* 响应式隐藏 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 特殊样式 */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
}

.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .card-header {
    @apply border-gray-700;
  }
  
  .card-footer {
    @apply border-gray-700 bg-gray-900;
  }
  
  .workspace-item {
    @apply hover:bg-gray-700;
  }
  
  .workspace-item.active {
    @apply bg-blue-900 border-blue-700;
  }
  
  .tab-item {
    @apply hover:bg-gray-700;
  }
  
  .tab-item.active {
    @apply bg-blue-900 border-blue-700;
  }
}
