import React from 'react';
import { clsx } from 'clsx';
import { WorkspaceTab } from '@/types';
import { extractSiteName, formatTime } from '@/utils';
import { Button } from './Button';
import { Pin, PinOff, X, Pause, Play, ExternalLink } from 'lucide-react';

interface TabItemProps {
  tab: chrome.tabs.Tab;
  isActive?: boolean;
  isPinned?: boolean;
  isSuspended?: boolean;
  onActivate?: (tabId: number) => void;
  onClose?: (tabId: number) => void;
  onPin?: (tabId: number) => void;
  onUnpin?: (tabId: number) => void;
  onSuspend?: (tabId: number) => void;
  className?: string;
}

export function TabItem({
  tab,
  isActive = false,
  isPinned = false,
  isSuspended = false,
  onActivate,
  onClose,
  onPin,
  onUnpin,
  onSuspend,
  className
}: TabItemProps) {
  const [showActions, setShowActions] = React.useState(false);

  const handleActivate = () => {
    if (tab.id && onActivate) {
      onActivate(tab.id);
    }
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (tab.id && onClose) {
      onClose(tab.id);
    }
  };

  const handlePin = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (tab.id) {
      if (isPinned && onUnpin) {
        onUnpin(tab.id);
      } else if (!isPinned && onPin) {
        onPin(tab.id);
      }
    }
  };

  const handleSuspend = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (tab.id && onSuspend) {
      onSuspend(tab.id);
    }
  };

  const siteName = tab.url ? extractSiteName(tab.url) : '';
  const favicon = tab.favIconUrl || '/icons/default-favicon.png';

  return (
    <div
      className={clsx(
        'tab-item group',
        isActive && 'active',
        isPinned && 'pinned',
        isSuspended && 'suspended',
        className
      )}
      onClick={handleActivate}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* 网站图标 */}
      <div className="flex-shrink-0 w-4 h-4 mr-3">
        <img
          src={favicon}
          alt=""
          className="w-full h-full rounded-sm"
          onError={(e) => {
            (e.target as HTMLImageElement).src = '/icons/default-favicon.png';
          }}
        />
      </div>
      
      {/* 标签页信息 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {tab.title || '无标题'}
          </h4>
          
          {/* 状态指示器 */}
          <div className="flex items-center space-x-1 ml-2">
            {isPinned && (
              <Pin className="w-3 h-3 text-yellow-600" title="已固定" />
            )}
            {isSuspended && (
              <Pause className="w-3 h-3 text-gray-500" title="已挂起" />
            )}
            {isActive && (
              <div className="w-2 h-2 bg-blue-500 rounded-full" title="当前活跃" />
            )}
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-1">
          <p className="text-xs text-gray-500 truncate">
            {siteName}
          </p>
          
          {tab.lastAccessed && (
            <span className="text-xs text-gray-400">
              {formatTime(tab.lastAccessed)}
            </span>
          )}
        </div>
      </div>
      
      {/* 操作按钮 */}
      {showActions && (
        <div className="flex items-center space-x-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
          {/* 固定/取消固定 */}
          <Button
            size="sm"
            variant="ghost"
            icon={isPinned ? <PinOff className="w-3 h-3" /> : <Pin className="w-3 h-3" />}
            onClick={handlePin}
            title={isPinned ? '取消固定' : '固定标签页'}
          />
          
          {/* 挂起/恢复 */}
          {!isSuspended && !isActive && (
            <Button
              size="sm"
              variant="ghost"
              icon={<Pause className="w-3 h-3" />}
              onClick={handleSuspend}
              title="挂起标签页"
            />
          )}
          
          {isSuspended && (
            <Button
              size="sm"
              variant="ghost"
              icon={<Play className="w-3 h-3" />}
              onClick={handleActivate}
              title="恢复标签页"
            />
          )}
          
          {/* 在新窗口打开 */}
          {tab.url && (
            <Button
              size="sm"
              variant="ghost"
              icon={<ExternalLink className="w-3 h-3" />}
              onClick={(e) => {
                e.stopPropagation();
                chrome.tabs.create({ url: tab.url });
              }}
              title="在新标签页打开"
            />
          )}
          
          {/* 关闭标签页 */}
          {!isPinned && (
            <Button
              size="sm"
              variant="ghost"
              icon={<X className="w-3 h-3" />}
              onClick={handleClose}
              title="关闭标签页"
            />
          )}
        </div>
      )}
    </div>
  );
}
