import React from 'react';
import { clsx } from 'clsx';
import { PresetWorkspace } from '@/types';
import { Button } from './Button';
import { Plus, Globe, Settings } from 'lucide-react';

interface PresetCardProps {
  preset: PresetWorkspace;
  onCreateWorkspace?: (presetId: string) => void;
  onEdit?: (preset: PresetWorkspace) => void;
  className?: string;
}

export function PresetCard({
  preset,
  onCreateWorkspace,
  onEdit,
  className
}: PresetCardProps) {
  const handleCreate = () => {
    if (onCreateWorkspace) {
      onCreateWorkspace(preset.id);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(preset);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'ai-tools': 'text-emerald-600 bg-emerald-50 border-emerald-200',
      'tech-forums': 'text-amber-600 bg-amber-50 border-amber-200',
      'collaboration': 'text-violet-600 bg-violet-50 border-violet-200',
      'development': 'text-blue-600 bg-blue-50 border-blue-200',
      'custom': 'text-gray-600 bg-gray-50 border-gray-200'
    };
    return colors[category] || colors.custom;
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      'ai-tools': 'AI工具',
      'tech-forums': '技术论坛',
      'collaboration': '协作工具',
      'development': '开发工具',
      'custom': '自定义'
    };
    return labels[category] || '未知';
  };

  return (
    <div
      className={clsx(
        'preset-item group',
        `preset-${preset.category}`,
        className
      )}
      onClick={handleCreate}
    >
      {/* 头部 */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center">
          <span className="text-2xl mr-3">{preset.icon}</span>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">
              {preset.name}
            </h3>
            <span className={clsx(
              'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full',
              getCategoryColor(preset.category)
            )}>
              {getCategoryLabel(preset.category)}
            </span>
          </div>
        </div>
        
        {/* 编辑按钮 */}
        <Button
          size="sm"
          variant="ghost"
          icon={<Settings className="w-3 h-3" />}
          onClick={handleEdit}
          className="opacity-0 group-hover:opacity-100 transition-opacity"
          title="编辑预设"
        />
      </div>
      
      {/* 描述 */}
      {preset.description && (
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {preset.description}
        </p>
      )}
      
      {/* URL列表预览 */}
      <div className="mb-4">
        <div className="flex items-center text-xs text-gray-500 mb-2">
          <Globe className="w-3 h-3 mr-1" />
          <span>{preset.urls.length} 个网站</span>
        </div>
        
        <div className="space-y-1">
          {preset.urls.slice(0, 3).map((url, index) => {
            const domain = new URL(url).hostname;
            return (
              <div key={index} className="text-xs text-gray-600 truncate">
                {domain}
              </div>
            );
          })}
          
          {preset.urls.length > 3 && (
            <div className="text-xs text-gray-500">
              还有 {preset.urls.length - 3} 个网站...
            </div>
          )}
        </div>
      </div>
      
      {/* 设置预览 */}
      {preset.settings && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {preset.settings.autoSuspend && (
              <span className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                自动挂起
              </span>
            )}
            {preset.settings.autoPinPatterns && preset.settings.autoPinPatterns.length > 0 && (
              <span className="inline-flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">
                自动固定
              </span>
            )}
            {preset.settings.allowDuplicates === false && (
              <span className="inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                去重
              </span>
            )}
          </div>
        </div>
      )}
      
      {/* 创建按钮 */}
      <Button
        variant="primary"
        size="sm"
        icon={<Plus className="w-4 h-4" />}
        className="w-full"
        onClick={handleCreate}
      >
        创建工作区
      </Button>
    </div>
  );
}
