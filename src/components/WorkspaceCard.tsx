import React from 'react';
import { clsx } from 'clsx';
import { Workspace } from '@/types';
import { formatTime, calculateWorkspaceStats, getWorkspaceColorClass } from '@/utils';
import { Button } from './Button';
import { MoreHorizontal, Play, Pause, Trash2, Edit3 } from 'lucide-react';

interface WorkspaceCardProps {
  workspace: Workspace;
  isActive?: boolean;
  onSwitch?: (workspaceId: string) => void;
  onEdit?: (workspace: Workspace) => void;
  onDelete?: (workspaceId: string) => void;
  onSuspend?: (workspaceId: string) => void;
  className?: string;
}

export function WorkspaceCard({
  workspace,
  isActive = false,
  onSwitch,
  onEdit,
  onDelete,
  onSuspend,
  className
}: WorkspaceCardProps) {
  const stats = calculateWorkspaceStats(workspace);
  const [showActions, setShowActions] = React.useState(false);

  const handleSwitch = () => {
    if (!isActive && onSwitch) {
      onSwitch(workspace.id);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(workspace);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(workspace.id);
    }
  };

  const handleSuspend = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSuspend) {
      onSuspend(workspace.id);
    }
  };

  return (
    <div
      className={clsx(
        'workspace-item group relative',
        isActive && 'active',
        className
      )}
      onClick={handleSwitch}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* 工作区颜色指示器 */}
      <div
        className={clsx(
          'workspace-indicator',
          getWorkspaceColorClass(workspace.color)
        )}
      />
      
      {/* 工作区信息 */}
      <div className="flex-1 ml-3 min-w-0">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-900 truncate">
            {workspace.icon && (
              <span className="mr-2">{workspace.icon}</span>
            )}
            {workspace.name}
          </h3>
          
          {isActive && (
            <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
              活跃
            </span>
          )}
        </div>
        
        {workspace.description && (
          <p className="text-xs text-gray-500 truncate mt-1">
            {workspace.description}
          </p>
        )}
        
        {/* 统计信息 */}
        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
          <span>{stats.totalTabs} 标签页</span>
          {stats.activeTabs > 0 && (
            <span>{stats.activeTabs} 活跃</span>
          )}
          {stats.pinnedTabs > 0 && (
            <span>{stats.pinnedTabs} 固定</span>
          )}
          <span>更新于 {formatTime(workspace.updatedAt)}</span>
        </div>
      </div>
      
      {/* 操作按钮 */}
      {showActions && (
        <div className="absolute right-2 top-2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {!isActive && (
            <Button
              size="sm"
              variant="ghost"
              icon={<Play className="w-3 h-3" />}
              onClick={handleSwitch}
              title="切换到此工作区"
            >
              切换
            </Button>
          )}
          
          {stats.activeTabs > 0 && (
            <Button
              size="sm"
              variant="ghost"
              icon={<Pause className="w-3 h-3" />}
              onClick={handleSuspend}
              title="挂起标签页"
            >
              挂起
            </Button>
          )}
          
          <Button
            size="sm"
            variant="ghost"
            icon={<Edit3 className="w-3 h-3" />}
            onClick={handleEdit}
            title="编辑工作区"
          >
            编辑
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            icon={<Trash2 className="w-3 h-3" />}
            onClick={handleDelete}
            title="删除工作区"
          >
            删除
          </Button>
        </div>
      )}
      
      {/* 内存使用指示器 */}
      {stats.memoryUsage > 100 && (
        <div className="absolute bottom-1 right-1">
          <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" title={`估计内存使用: ${stats.memoryUsage}MB`} />
        </div>
      )}
    </div>
  );
}
