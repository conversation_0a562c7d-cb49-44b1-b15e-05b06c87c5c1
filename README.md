# AI工作台 - Chrome工作区管理扩展

> 专业的Chrome标签页管理和AI工具工作空间扩展，类似Workona的智能工作区管理解决方案

## 🎯 产品概述

AI工作台是一个专为AI工具重度使用者、技术工作者和知识工作者设计的Chrome扩展，提供智能的工作区管理和标签页组织功能。

### 核心特性

- 🏢 **多工作区管理** - 创建、切换、删除工作区，保存和恢复工作状态
- 📌 **智能标签页管理** - 自动固定、分组、挂起/恢复、重复检测
- 🎯 **预设工作区** - 一键创建AI工具、技术论坛、协作工具等常用环境
- 💾 **数据同步** - 本地存储、备份恢复、数据导入导出
- 🎨 **现代界面** - React + TypeScript + Tailwind CSS

## 🚀 快速开始

### 环境要求

- Node.js 16+
- Chrome 88+（支持Manifest V3）

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 加载扩展

1. 打开Chrome扩展管理页面 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择项目的 `dist` 目录

## 📁 项目结构

```
aitab/
├── src/
│   ├── background/          # 后台脚本
│   ├── content/            # 内容脚本
│   ├── popup/              # 弹出窗口
│   ├── sidepanel/          # 侧边栏
│   ├── components/         # React组件
│   ├── hooks/              # React Hooks
│   ├── services/           # 业务服务
│   ├── types/              # TypeScript类型
│   ├── utils/              # 工具函数
│   ├── styles/             # 样式文件
│   └── icons/              # 图标资源
├── manifest.json           # 扩展清单
├── webpack.config.js       # Webpack配置
├── tailwind.config.js      # Tailwind配置
└── package.json
```

## 🔧 核心功能

### 工作区管理

- **创建工作区**: 支持自定义名称、描述、颜色
- **切换工作区**: 保存当前状态，恢复目标工作区
- **工作区设置**: 自动挂起、固定模式、去重等

### 标签页管理

- **智能固定**: 基于URL模式自动固定重要标签页
- **标签页挂起**: 释放内存，提升性能
- **重复检测**: 自动识别并处理重复标签页
- **分组管理**: 创建标签页分组，提升组织效率

### 预设工作区

#### AI工具主力
- ChatGPT
- Gemini
- LobeHub
- Perplexity
- Grok
- AI Studio

#### AI次选工具
- DeepAsk
- GPTFun
- Claude
- Poe

#### 技术论坛
- Linux.do
- NodeLoc
- NodeSeek
- GitHub
- Stack Overflow

#### 协作工具
- 语雀
- 飞书
- Notion
- Slack

### 数据管理

- **本地存储**: 基于Chrome Storage API
- **数据备份**: 自动创建和管理备份
- **导入导出**: JSON格式数据交换
- **存储优化**: 自动清理和空间管理

## 🎨 界面设计

### Popup界面
- 紧凑的400x600像素设计
- 工作区、预设、标签页三个主要标签
- 搜索功能和快速操作

### 侧边栏
- 全高度侧边栏界面
- 详细的工作区和标签页管理
- 实时统计和状态显示

## ⌨️ 快捷键

- `Ctrl+Shift+W` (Mac: `Cmd+Shift+W`) - 快速切换工作区
- `Ctrl+Shift+N` (Mac: `Cmd+Shift+N`) - 创建新工作区
- `Ctrl+Shift+S` (Mac: `Cmd+Shift+S`) - 挂起当前工作区标签页

## 🔧 开发指南

### 技术栈

- **前端框架**: React 18 + TypeScript
- **样式框架**: Tailwind CSS
- **构建工具**: Webpack 5
- **代码规范**: ESLint + Prettier
- **图标库**: Lucide React

### 架构设计

- **Manifest V3**: 使用最新的Chrome扩展API
- **Service Worker**: 后台处理和消息通信
- **模块化设计**: 服务层、Hook层、组件层分离
- **类型安全**: 完整的TypeScript类型定义

### 开发命令

```bash
# 开发模式（监听文件变化）
npm run dev

# 生产构建
npm run build

# 开发构建
npm run build:dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 自动修复代码风格
npm run lint:fix
```

## 📊 性能优化

- **标签页挂起**: 自动挂起非活跃标签页，释放内存
- **懒加载**: 组件和资源按需加载
- **代码分割**: Webpack自动分割vendor和业务代码
- **存储优化**: 定期清理临时数据和旧备份

## 🔒 隐私安全

- **本地存储**: 所有数据存储在本地，不上传到服务器
- **权限最小化**: 只请求必要的Chrome API权限
- **数据加密**: 敏感数据可选择加密存储
- **用户控制**: 完全的数据导入导出控制权

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Chrome Extensions API](https://developer.chrome.com/docs/extensions/)
- [React](https://reactjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Lucide Icons](https://lucide.dev/)

## 📞 支持

如果您遇到问题或有功能建议，请：

1. 查看 [Issues](https://github.com/your-repo/aitab/issues)
2. 创建新的 Issue
3. 联系开发团队

---

**AI工作台** - 让您的Chrome工作区更智能、更高效！
