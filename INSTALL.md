# AI工作台 - 安装指南

## 🚀 快速安装

### 方法一：从源码构建（推荐）

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd aitab
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **构建项目**
   ```bash
   npm run build
   ```

4. **加载到Chrome**
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目的 `dist` 目录

### 方法二：开发模式

如果你想要修改代码或进行开发：

1. **启动开发模式**
   ```bash
   npm run dev
   ```

2. **加载扩展**
   - 按照上述步骤4加载 `dist` 目录
   - 每次代码更改后，点击扩展的"重新加载"按钮

## 📋 系统要求

- **Node.js**: 16.0 或更高版本
- **Chrome**: 88 或更高版本（支持Manifest V3）
- **操作系统**: Windows、macOS、Linux

## 🔧 验证安装

安装成功后，你应该能看到：

1. **扩展图标**: Chrome工具栏中出现AI工作台图标
2. **弹出窗口**: 点击图标显示工作区管理界面
3. **快捷键**: 
   - `Ctrl+Shift+W` (Mac: `Cmd+Shift+W`) - 快速切换工作区
   - `Ctrl+Shift+N` (Mac: `Cmd+Shift+N`) - 创建新工作区
   - `Ctrl+Shift+S` (Mac: `Cmd+Shift+S`) - 挂起标签页

## 🎯 首次使用

1. **创建第一个工作区**
   - 点击扩展图标
   - 点击"新建"按钮
   - 输入工作区名称和描述

2. **使用预设工作区**
   - 切换到"预设"标签
   - 选择"AI工具套件"或"完整环境"
   - 点击"创建工作区"

3. **管理标签页**
   - 切换到"标签页"标签
   - 使用固定、挂起、去重等功能

## 🛠️ 故障排除

### 扩展无法加载
- 确保Chrome版本支持Manifest V3
- 检查是否开启了"开发者模式"
- 确认选择的是 `dist` 目录而不是项目根目录

### 构建失败
- 检查Node.js版本是否符合要求
- 删除 `node_modules` 文件夹后重新安装依赖
- 运行 `npm run type-check` 检查TypeScript错误

### 功能异常
- 打开Chrome开发者工具查看控制台错误
- 检查扩展权限是否正确授予
- 尝试重新加载扩展

## 📞 获取帮助

如果遇到问题：

1. 查看 [常见问题](FAQ.md)
2. 搜索 [Issues](https://github.com/your-repo/aitab/issues)
3. 创建新的Issue描述问题

## 🔄 更新扩展

当有新版本时：

1. **拉取最新代码**
   ```bash
   git pull origin main
   ```

2. **重新构建**
   ```bash
   npm install
   npm run build
   ```

3. **重新加载扩展**
   - 在 `chrome://extensions/` 页面
   - 点击AI工作台扩展的"重新加载"按钮

## 🗑️ 卸载

如需卸载扩展：

1. 访问 `chrome://extensions/`
2. 找到"AI工作台"扩展
3. 点击"移除"按钮
4. 确认删除

**注意**: 卸载会删除所有本地数据，建议先导出数据备份。

---

🎉 **安装完成！开始享受高效的工作区管理体验吧！**
